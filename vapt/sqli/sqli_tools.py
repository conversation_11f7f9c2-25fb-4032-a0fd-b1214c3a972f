"""
Enhanced SQLi Testing Tools - Specialized, Self-Validating Functions

These tools provide grounded, deterministic testing for different SQLi techniques
to prevent LLM hallucination and ensure reliable vulnerability detection.
"""

import time
import re
import requests
from typing import Dict, Any, List, Optional, Tuple
from urllib.parse import parse_qs, urlparse, urlencode, urlunparse
from agno.tools import tool


def _parse_raw_request(raw_request: str) -> Dict[str, Any]:
    """Parse a raw HTTP request into components"""
    lines = raw_request.strip().split('\n')
    if not lines:
        return {}
    
    # Parse request line
    request_line = lines[0].strip()
    parts = request_line.split(' ')
    if len(parts) < 3:
        return {}
    
    method = parts[0]
    path = parts[1]
    
    # Parse headers
    headers = {}
    body = ""
    body_start = -1
    
    for i, line in enumerate(lines[1:], 1):
        if line.strip() == "":
            body_start = i + 1
            break
        if ':' in line:
            key, value = line.split(':', 1)
            headers[key.strip()] = value.strip()
    
    # Parse body if present
    if body_start > 0 and body_start < len(lines):
        body = '\n'.join(lines[body_start:])
    
    # Extract host for URL construction
    host = headers.get('Host', 'localhost')
    scheme = 'https' if headers.get('X-Forwarded-Proto') == 'https' else 'http'
    url = f"{scheme}://{host}{path}"
    
    return {
        'method': method,
        'url': url,
        'headers': headers,
        'body': body,
        'path': path
    }


def _send_request(method: str, url: str, headers: Dict[str, str], 
                 data: Optional[str] = None, timeout: int = 30) -> Tuple[requests.Response, float]:
    """Send HTTP request and measure response time"""
    start_time = time.time()
    try:
        response = requests.request(
            method=method,
            url=url,
            headers=headers,
            data=data,
            timeout=timeout,
            verify=False,
            allow_redirects=True
        )
        end_time = time.time()
        return response, end_time - start_time
    except Exception as e:
        end_time = time.time()
        # Create a mock response for error cases
        mock_response = requests.Response()
        mock_response.status_code = 0
        mock_response._content = str(e).encode()
        return mock_response, end_time - start_time


@tool
def test_time_based_sqli(raw_request: str, payload: str, delay_seconds: int = 5) -> Dict[str, Any]:
    """
    Test for time-based SQL injection by comparing response times
    
    Args:
        raw_request: Raw HTTP request string
        payload: SQL injection payload that should cause a delay
        delay_seconds: Expected delay in seconds (default: 5)
        
    Returns:
        Structured JSON with vulnerability assessment
    """
    request_data = _parse_raw_request(raw_request)
    if not request_data:
        return {
            "is_vulnerable": False,
            "error": "Failed to parse raw request",
            "conclusion": "Request parsing failed"
        }
    
    # Send baseline request
    baseline_response, baseline_duration = _send_request(
        request_data['method'],
        request_data['url'],
        request_data['headers'],
        request_data['body']
    )
    
    # Inject payload into URL parameters or body
    if request_data['method'].upper() == 'GET':
        # Inject into URL parameters
        parsed_url = urlparse(request_data['url'])
        params = parse_qs(parsed_url.query)
        
        # Try to inject into first parameter
        if params:
            first_param = list(params.keys())[0]
            params[first_param] = [params[first_param][0] + payload]
            new_query = urlencode(params, doseq=True)
            payload_url = urlunparse(parsed_url._replace(query=new_query))
        else:
            payload_url = request_data['url'] + f"?test={payload}"
        
        payload_response, payload_duration = _send_request(
            request_data['method'],
            payload_url,
            request_data['headers']
        )
    else:
        # Inject into POST body
        payload_body = request_data['body'] + f"&test={payload}" if request_data['body'] else f"test={payload}"
        payload_response, payload_duration = _send_request(
            request_data['method'],
            request_data['url'],
            request_data['headers'],
            payload_body
        )
    
    # Analyze time difference
    time_difference = payload_duration - baseline_duration
    is_vulnerable = time_difference >= (delay_seconds * 0.8)  # 80% threshold for reliability
    
    conclusion = (
        f"Time-based SQLi CONFIRMED: Payload caused {time_difference:.2f}s delay (expected: {delay_seconds}s)"
        if is_vulnerable else
        f"Time-based SQLi NOT confirmed: Only {time_difference:.2f}s difference (expected: {delay_seconds}s)"
    )
    
    return {
        "is_vulnerable": is_vulnerable,
        "baseline_duration_sec": round(baseline_duration, 3),
        "payload_duration_sec": round(payload_duration, 3),
        "time_difference_sec": round(time_difference, 3),
        "expected_delay_sec": delay_seconds,
        "payload_used": payload,
        "conclusion": conclusion
    }


@tool
def test_error_based_sqli(raw_request: str, payload: str, error_patterns: List[str]) -> Dict[str, Any]:
    """
    Test for error-based SQL injection by looking for database error messages
    
    Args:
        raw_request: Raw HTTP request string
        payload: SQL injection payload designed to trigger errors
        error_patterns: List of regex patterns to search for database errors
        
    Returns:
        Structured JSON with vulnerability assessment
    """
    request_data = _parse_raw_request(raw_request)
    if not request_data:
        return {
            "is_vulnerable": False,
            "error": "Failed to parse raw request",
            "conclusion": "Request parsing failed"
        }
    
    # Default error patterns if none provided
    if not error_patterns:
        error_patterns = [
            r"ORA-[0-9]{5}",  # Oracle
            r"MySQL.*Error",  # MySQL
            r"PostgreSQL.*ERROR",  # PostgreSQL
            r"Microsoft.*ODBC.*SQL Server",  # SQL Server
            r"syntax error at or near",  # PostgreSQL
            r"You have an error in your SQL syntax",  # MySQL
            r"Unclosed quotation mark",  # SQL Server
            r"sqlite3\\.OperationalError",  # SQLite
            r"Warning.*mysql_.*",  # MySQL warnings
            r"valid MySQL result",  # MySQL
            r"MySqlClient\\.",  # MySQL .NET
        ]
    
    # Inject payload and send request
    if request_data['method'].upper() == 'GET':
        parsed_url = urlparse(request_data['url'])
        params = parse_qs(parsed_url.query)
        
        if params:
            first_param = list(params.keys())[0]
            params[first_param] = [params[first_param][0] + payload]
            new_query = urlencode(params, doseq=True)
            payload_url = urlunparse(parsed_url._replace(query=new_query))
        else:
            payload_url = request_data['url'] + f"?test={payload}"
        
        response, duration = _send_request(
            request_data['method'],
            payload_url,
            request_data['headers']
        )
    else:
        payload_body = request_data['body'] + f"&test={payload}" if request_data['body'] else f"test={payload}"
        response, duration = _send_request(
            request_data['method'],
            request_data['url'],
            request_data['headers'],
            payload_body
        )
    
    # Search for error patterns in response
    response_text = response.text if hasattr(response, 'text') else str(response.content)
    matched_pattern = None
    response_snippet = None
    
    for pattern in error_patterns:
        match = re.search(pattern, response_text, re.IGNORECASE)
        if match:
            matched_pattern = pattern
            # Extract snippet around the match
            start = max(0, match.start() - 50)
            end = min(len(response_text), match.end() + 50)
            response_snippet = response_text[start:end]
            break
    
    is_vulnerable = matched_pattern is not None
    conclusion = (
        f"Error-based SQLi CONFIRMED: Found database error matching pattern '{matched_pattern}'"
        if is_vulnerable else
        "Error-based SQLi NOT confirmed: No database error patterns found in response"
    )
    
    return {
        "is_vulnerable": is_vulnerable,
        "matched_pattern": matched_pattern,
        "response_snippet": response_snippet,
        "payload_used": payload,
        "patterns_tested": error_patterns,
        "response_status_code": response.status_code if hasattr(response, 'status_code') else 0,
        "conclusion": conclusion
    }


@tool
def test_union_based_sqli(raw_request: str, max_columns: int = 10) -> Dict[str, Any]:
    """
    Test for UNION-based SQL injection by finding the correct number of columns

    Args:
        raw_request: Raw HTTP request string
        max_columns: Maximum number of columns to test (default: 10)

    Returns:
        Structured JSON with vulnerability assessment
    """
    request_data = _parse_raw_request(raw_request)
    if not request_data:
        return {
            "is_vulnerable": False,
            "error": "Failed to parse raw request",
            "conclusion": "Request parsing failed"
        }

    # Send baseline request to get original response
    baseline_response, baseline_duration = _send_request(
        request_data['method'],
        request_data['url'],
        request_data['headers'],
        request_data['body']
    )

    baseline_length = len(baseline_response.text) if hasattr(baseline_response, 'text') else 0
    baseline_status = baseline_response.status_code if hasattr(baseline_response, 'status_code') else 0

    # Test different column counts
    for column_count in range(1, max_columns + 1):
        # Create UNION payload
        null_values = ','.join(['NULL'] * column_count)
        payload = f"' UNION SELECT {null_values}--"

        # Send request with UNION payload
        if request_data['method'].upper() == 'GET':
            parsed_url = urlparse(request_data['url'])
            params = parse_qs(parsed_url.query)

            if params:
                first_param = list(params.keys())[0]
                params[first_param] = [params[first_param][0] + payload]
                new_query = urlencode(params, doseq=True)
                payload_url = urlunparse(parsed_url._replace(query=new_query))
            else:
                payload_url = request_data['url'] + f"?test={payload}"

            response, duration = _send_request(
                request_data['method'],
                payload_url,
                request_data['headers']
            )
        else:
            payload_body = request_data['body'] + f"&test={payload}" if request_data['body'] else f"test={payload}"
            response, duration = _send_request(
                request_data['method'],
                request_data['url'],
                request_data['headers'],
                payload_body
            )

        response_length = len(response.text) if hasattr(response, 'text') else 0
        response_status = response.status_code if hasattr(response, 'status_code') else 0

        # Check for successful UNION injection indicators
        # 1. Status code changed from error to success
        # 2. Significant change in response length
        # 3. Response contains expected NULL values or different content

        length_difference = abs(response_length - baseline_length)
        status_improved = baseline_status >= 400 and response_status < 400
        significant_length_change = length_difference > (baseline_length * 0.1)  # 10% change threshold

        if status_improved or significant_length_change:
            conclusion = f"UNION-based SQLi CONFIRMED: Found correct column count of {column_count}"
            return {
                "is_vulnerable": True,
                "correct_column_count": column_count,
                "payload_used": payload,
                "baseline_length": baseline_length,
                "payload_response_length": response_length,
                "length_difference": length_difference,
                "baseline_status": baseline_status,
                "payload_status": response_status,
                "conclusion": conclusion
            }

    conclusion = f"UNION-based SQLi NOT confirmed: Tested {max_columns} column counts without success"
    return {
        "is_vulnerable": False,
        "correct_column_count": None,
        "max_columns_tested": max_columns,
        "baseline_length": baseline_length,
        "baseline_status": baseline_status,
        "conclusion": conclusion
    }


@tool
def test_boolean_based_sqli(raw_request: str, true_payload: str = "' AND 1=1--",
                           false_payload: str = "' AND 1=2--") -> Dict[str, Any]:
    """
    Test for boolean-based SQL injection by comparing TRUE vs FALSE condition responses

    Args:
        raw_request: Raw HTTP request string
        true_payload: Payload that should evaluate to TRUE
        false_payload: Payload that should evaluate to FALSE

    Returns:
        Structured JSON with vulnerability assessment
    """
    request_data = _parse_raw_request(raw_request)
    if not request_data:
        return {
            "is_vulnerable": False,
            "error": "Failed to parse raw request",
            "conclusion": "Request parsing failed"
        }

    # Send request with TRUE condition
    if request_data['method'].upper() == 'GET':
        parsed_url = urlparse(request_data['url'])
        params = parse_qs(parsed_url.query)

        if params:
            first_param = list(params.keys())[0]
            # TRUE condition
            params_true = params.copy()
            params_true[first_param] = [params_true[first_param][0] + true_payload]
            true_query = urlencode(params_true, doseq=True)
            true_url = urlunparse(parsed_url._replace(query=true_query))

            # FALSE condition
            params_false = params.copy()
            params_false[first_param] = [params_false[first_param][0] + false_payload]
            false_query = urlencode(params_false, doseq=True)
            false_url = urlunparse(parsed_url._replace(query=false_query))
        else:
            true_url = request_data['url'] + f"?test={true_payload}"
            false_url = request_data['url'] + f"?test={false_payload}"

        true_response, true_duration = _send_request(
            request_data['method'], true_url, request_data['headers']
        )
        false_response, false_duration = _send_request(
            request_data['method'], false_url, request_data['headers']
        )
    else:
        true_body = request_data['body'] + f"&test={true_payload}" if request_data['body'] else f"test={true_payload}"
        false_body = request_data['body'] + f"&test={false_payload}" if request_data['body'] else f"test={false_payload}"

        true_response, true_duration = _send_request(
            request_data['method'], request_data['url'], request_data['headers'], true_body
        )
        false_response, false_duration = _send_request(
            request_data['method'], request_data['url'], request_data['headers'], false_body
        )

    # Compare responses
    true_text = true_response.text if hasattr(true_response, 'text') else str(true_response.content)
    false_text = false_response.text if hasattr(false_response, 'text') else str(false_response.content)

    true_length = len(true_text)
    false_length = len(false_text)
    content_is_different = true_text != false_text

    # Additional checks
    true_status = true_response.status_code if hasattr(true_response, 'status_code') else 0
    false_status = false_response.status_code if hasattr(false_response, 'status_code') else 0
    status_different = true_status != false_status

    is_vulnerable = content_is_different or status_different

    if is_vulnerable:
        conclusion = "Boolean-based SQLi CONFIRMED: TRUE and FALSE conditions produced different responses"
    else:
        conclusion = "Boolean-based SQLi NOT confirmed: TRUE and FALSE conditions produced identical responses"

    return {
        "is_vulnerable": is_vulnerable,
        "true_payload": true_payload,
        "false_payload": false_payload,
        "response_true_len": true_length,
        "response_false_len": false_length,
        "true_status_code": true_status,
        "false_status_code": false_status,
        "content_is_different": content_is_different,
        "status_is_different": status_different,
        "conclusion": conclusion
    }
