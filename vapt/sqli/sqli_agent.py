"""
SQLi Agent - SQL Injection Testing

Specialized agent for detecting and exploiting SQL injection vulnerabilities.
Follows PentestAI architecture with automated and manual testing capabilities.
"""

import json
import asyncio
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

from agno.agent import Agent

import sys
from pathlib import Path

# Add parent directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from shared.config import VAPTConfig, get_vapt_config
from shared.llm_factory import LLMFactory
from shared.logger import get_logger
from shared.python_executor import execute_python_code
from shared.llm_logging_utils import (
    create_agent_llm_logger,
    log_batch_processing_details,
    format_vulnerability_results_for_logging,
    log_agent_performance_metrics
)
from vapt.sqli.sqli_scanner import sqli_scanner_tool
from vapt.sqli.sqli_tools import (
    test_time_based_sqli,
    test_error_based_sqli,
    test_union_based_sqli,
    test_boolean_based_sqli
)

# Enhanced SQLi Agent System Prompt with specialized tools
SQLI_SYSTEM_PROMPT = """
You are a methodical SQLI Agent. Your goal is to test for SQL injection by using a suite of specialized, self-validating tools.

**YOUR PRIMARY DIRECTIVE: USE THE RIGHT TOOL AND TRUST ITS OUTPUT**
Your ONLY method for testing is to use the provided tools (`test_time_based_sqli`, `test_error_based_sqli`, `test_union_based_sqli`, `test_boolean_based_sqli`). The 'conclusion' field in the tool's JSON output is the source of truth. You MUST NOT report a vulnerability unless a tool explicitly confirms it with `"is_vulnerable": true`.

Available Tools:
--------------
1. `test_time_based_sqli` - Tests for time-based blind SQL injection by measuring response delays
2. `test_error_based_sqli` - Tests for error-based SQL injection by detecting database error messages
3. `test_union_based_sqli` - Tests for UNION-based SQL injection by finding correct column counts
4. `test_boolean_based_sqli` - Tests for boolean-based blind SQL injection by comparing TRUE/FALSE responses
5. `sqli_scanner_tool` - Automated SQL injection scanner using sqlmap (use as backup)
6. `execute_python_code` - ONLY for complex, multi-step exploits AFTER vulnerability confirmation

**General Testing Workflow:**
1. **Analyze Batch & Prioritize**: Review the entire batch of requests to identify the most promising candidates for testing.
2. **Form a Hypothesis**: For a given target, decide which type of SQLi is most likely. Is there a search parameter? Try error-based. Does the page have a simple ID? Try boolean-based.
3. **Select and Use the Correct Tool**: Call the specific tool that matches your hypothesis (e.g., `test_error_based_sqli`).
4. **Analyze the Tool's JSON Output**: Examine the structured JSON returned by the tool.
5. **Report Based on Evidence**: If the tool's output is `{"is_vulnerable": true, ...}`, construct a vulnerability report using the details from the output (e.g., `matched_pattern`, `payload_duration_sec`). If the output is `{"is_vulnerable": false, ...}`, you MUST conclude that the target is not vulnerable to that specific technique and move on.

**Testing Strategy by SQLi Type:**
- **Time-Based**: Use `test_time_based_sqli` with payloads like `' AND SLEEP(5)--` or `' OR 1=(SELECT 1 FROM PG_SLEEP(5))--`
- **Error-Based**: Use `test_error_based_sqli` with payloads like `'` or `' AND 1=CONVERT(int, 'text')--`
- **UNION-Based**: Use `test_union_based_sqli` to automatically find correct column count
- **Boolean-Based**: Use `test_boolean_based_sqli` with TRUE/FALSE condition payloads

**DO NOT USE `execute_python_code` for simple testing. It is a last resort for complex, multi-step exploits ONLY AFTER a vulnerability has been confirmed by a specialized tool.**

Rules:
------
- TRUST THE TOOL OUTPUT: If a specialized tool returns `"is_vulnerable": false`, do NOT claim a vulnerability exists
- Use the most appropriate tool for each hypothesis
- Only use `sqli_scanner_tool` if specialized tools are inconclusive
- Only use `execute_python_code` for exploitation AFTER confirming vulnerability with specialized tools
- Prioritize requests with injectable parameters (id, search, user, etc.)
- Apply successful techniques to similar requests in the batch
- Return results in the exact JSON format specified in expected_output
"""


def create_sqli_agent(model) -> Agent:
    """
    Create SQLi Agent following PentestAI pattern

    Args:
        model: LLM model instance

    Returns:
        Configured SQLi Agent
    """
    expected_output = """
    You MUST return your findings as a single JSON array of vulnerability objects using this exact schema:

    [
      {
        "type": "SQL Injection",
        "parameter": "<The vulnerable parameter name or 'N/A' if the whole endpoint>",
        "payload": "<The specific payload that proved the vulnerability>",
        "technique": "<The SQLi technique used, e.g., 'Error-based', 'Time-based', 'UNION-based'>",
        "curl_command": "<The full curl command with the payload to replicate the finding>",
        "evidence": "<A brief snippet from the response or description of the behavior that serves as proof of the vulnerability>",
        "reasoning": "<A brief explanation of why this is a vulnerability>"
      }
    ]

    If no vulnerabilities are found, return an empty array: []

    IMPORTANT: Your response must be valid JSON only. Do not include any other text, explanations, or formatting outside the JSON array.
    """

    agent = Agent(
        name="SQLI Agent",
        role="Specialized agent that detects and exploits SQL injection vulnerabilities",
        goal="Given an HTTP request/response pair, detect and exploit SQL-injection (SQLi) vulnerabilities",
        expected_output=expected_output,
        model=model,
        instructions=SQLI_SYSTEM_PROMPT,
        tools=[
            test_time_based_sqli,
            test_error_based_sqli,
            test_union_based_sqli,
            test_boolean_based_sqli,
            sqli_scanner_tool,
            execute_python_code
        ],
        debug_mode=True,
        show_tool_calls=True
    )
    return agent


class SQLiAgent:
    """
    SQL Injection Testing Agent

    Follows PentestAI architecture with automated and manual testing capabilities.
    """

    def __init__(self, config: Optional[VAPTConfig] = None):
        self.config = config or get_vapt_config()
        self.logger = get_logger("sqli_agent", self.config.log_level)
        self.llm = LLMFactory.create_llm(self.config.llm)

        # Initialize LLM logging
        self.llm_logger = create_agent_llm_logger("sqli_agent", "vulnerability_scanner")

        self.logger.info("SQLi Agent initialized with PentestAI architecture and LLM logging")

    async def scan_from_report(self, preprocessor_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Scan for SQL injection vulnerabilities using preprocessor report with batch processing

        Args:
            preprocessor_data: Data from browser agent reconnaissance

        Returns:
            Dictionary containing SQLi scan results
        """
        scan_start_time = time.time()
        self.logger.info("Starting SQLi vulnerability scan from preprocessor report")

        try:
            # Create SQLi agent
            agent = create_sqli_agent(self.llm)

            # Extract data from preprocessor report
            forms = preprocessor_data.get('forms', [])
            network_requests = preprocessor_data.get('network_logs', [])
            target_url = preprocessor_data.get('metadata', {}).get('target_url', 'unknown')

            # Collect all testable raw requests into batch_requests
            batch_requests = []

            # Add forms to batch
            for form in forms:
                if self._has_injectable_inputs(form):
                    form_info = {
                        'action': form.get('action', ''),
                        'method': form.get('method', 'GET'),
                        'inputs': form.get('inputs', []),
                        'target_url': target_url
                    }
                    raw_request = self._form_to_raw_request(form_info)
                    batch_requests.append(raw_request)

            # Add network requests to batch
            testable_requests = [req for req in network_requests if self._is_testable_request(req)]
            for request in testable_requests:
                raw_request = self._network_request_to_raw(request)
                batch_requests.append(raw_request)

            results = {
                'timestamp': datetime.now().isoformat(),
                'target_url': target_url,
                'forms_tested': len([f for f in forms if self._has_injectable_inputs(f)]),
                'requests_tested': len(testable_requests),
                'total_batch_requests': len(batch_requests),
                'vulnerabilities': [],
                'agent_response': None
            }

            if not batch_requests:
                self.logger.info("No testable requests found for SQLi scanning")
                results['message'] = "No testable requests found"

                # Log performance metrics
                scan_end_time = time.time()
                performance_metrics = log_agent_performance_metrics(
                    self.llm_logger, scan_start_time, scan_end_time,
                    "sqli_scan", True, {'reason': 'no_testable_requests'}
                )
                self.logger.info(f"SQLi scan performance: {performance_metrics}")

                return results

            # Log batch processing details
            batch_details = log_batch_processing_details(self.llm_logger, batch_requests, target_url)
            self.logger.info(f"Batch processing details: {batch_details}")

            # Make single API call with batch processing prompt
            self.logger.info(f"Processing batch of {len(batch_requests)} requests with single agent call")
            batch_prompt = self._create_batch_processing_prompt(batch_requests, target_url)

            # Log the LLM interaction with detailed metadata
            llm_metadata = {
                'batch_size': len(batch_requests),
                'forms_count': len([f for f in forms if self._has_injectable_inputs(f)]),
                'network_requests_count': len(testable_requests),
                'prompt_length': len(batch_prompt),
                'batch_details': batch_details
            }

            # Log prompt details
            prompt_info = self.llm_logger.log_prompt_details(batch_prompt, "batch_sqli_scan")
            self.logger.debug(f"Prompt details: {prompt_info}")

            # Make LLM call with logging
            with self.llm_logger.log_agent_llm_interaction(
                model=self.config.llm.model,
                provider=self.config.llm.provider,
                prompt=batch_prompt,
                interaction_type="batch_sqli_scan",
                target_url=target_url,
                additional_metadata=llm_metadata
            ) as llm_ctx:
                agent_response = await agent.arun(batch_prompt)

                # Extract content from response object
                if hasattr(agent_response, 'content'):
                    agent_response_content = agent_response.content
                elif hasattr(agent_response, 'text'):
                    agent_response_content = agent_response.text
                else:
                    agent_response_content = str(agent_response)

                # Complete the LLM interaction logging
                if hasattr(self.llm_logger, 'llm_logger') and self.llm_logger.llm_logger:
                    # Extract token usage if available
                    token_usage = None
                    if hasattr(agent_response, 'usage'):
                        token_usage = agent_response.usage

                    self.llm_logger.llm_logger.complete_interaction(llm_ctx, agent_response_content, token_usage)

            results['agent_response'] = agent_response_content

            # Parse JSON response
            vulnerabilities = self._parse_json_vulnerabilities(agent_response_content)
            results['vulnerabilities'] = vulnerabilities

            # Log vulnerability results
            vuln_summary = format_vulnerability_results_for_logging(vulnerabilities)
            self.logger.info(f"Vulnerability analysis results: {vuln_summary}")

            # Save results
            report_path = self._save_sqli_report(results, target_url)
            results["report_path"] = report_path

            # Log performance metrics
            scan_end_time = time.time()
            performance_metrics = log_agent_performance_metrics(
                self.llm_logger, scan_start_time, scan_end_time,
                "sqli_scan", True, {
                    'vulnerabilities_found': len(vulnerabilities),
                    'batch_size': len(batch_requests),
                    'llm_interactions': 1
                }
            )
            self.logger.info(f"SQLi scan performance: {performance_metrics}")

            self.logger.info(f"SQLi scan completed. Found {len(results['vulnerabilities'])} vulnerabilities")
            return results

        except Exception as e:
            scan_end_time = time.time()

            # Log error performance metrics
            performance_metrics = log_agent_performance_metrics(
                self.llm_logger, scan_start_time, scan_end_time,
                "sqli_scan", False, {'error': str(e)}
            )
            self.logger.error(f"SQLi scan failed. Performance: {performance_metrics}")

            self.logger.error(f"Error in SQLi scan: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'target_url': target_url if 'target_url' in locals() else 'unknown'
            }
    
    def _has_injectable_inputs(self, form: Dict[str, Any]) -> bool:
        """Check if form has potentially injectable inputs"""
        inputs = form.get('inputs', [])
        for input_field in inputs:
            input_type = input_field.get('type', '').lower()
            if input_type in ['text', 'search', 'email', 'url', 'tel', 'password', 'hidden']:
                return True
        return False

    def _is_testable_request(self, request: Dict[str, Any]) -> bool:
        """Check if network request is testable for SQLi"""
        method = request.get('method', '').upper()
        url = request.get('url', '')

        # Test POST requests and GET requests with parameters
        if method == 'POST':
            return True
        elif method == 'GET' and ('?' in url or 'id=' in url or 'search=' in url):
            return True

        return False

    def _create_batch_processing_prompt(self, batch_requests: List[str], target_url: str) -> str:
        """Create a comprehensive batch processing prompt for the agent"""
        return f"""
Perform comprehensive SQL injection testing on this batch of HTTP requests using intelligent batch processing.

TARGET: {target_url}
TOTAL REQUESTS: {len(batch_requests)}

BATCH REQUESTS TO ANALYZE:
{'='*50}
{chr(10).join([f"REQUEST {i+1}:{chr(10)}{req}{chr(10)}{'-'*30}" for i, req in enumerate(batch_requests)])}
{'='*50}

INTELLIGENT BATCH PROCESSING INSTRUCTIONS:
1. **Analyze the entire batch** - Review all {len(batch_requests)} requests to understand the application structure
2. **Smart Prioritization** - Focus on the most promising candidates first:
   - Endpoints with parameters like id=, user_id=, search=, category=, etc.
   - URLs containing /user/, /api/, /admin/, /search/, /product/
   - POST requests with form data or JSON payloads
   - Requests with multiple parameters
3. **Efficient Testing Strategy**:
   - Use sqli_scanner_tool for automated scanning of high-priority requests
   - Use execute_python_code for custom payload testing and analysis
   - Test error-based, UNION-based, boolean-based, and time-based techniques
4. **Apply Learning Across Requests**:
   - If a payload works on one endpoint, try it on similar endpoints
   - If a specific parameter type (e.g., 'id') is vulnerable, prioritize similar parameters
   - If a technique works, apply that technique to similar requests in the batch
5. **Comprehensive Coverage** - Ensure all promising requests are tested, but prioritize efficiently

FOCUS AREAS:
- GET/POST parameters (especially id, search, user, category, etc.)
- Headers (User-Agent, Referer, X-Forwarded-For, etc.)
- Cookies and session parameters
- JSON/XML data in POST bodies
- File upload parameters

TESTING WORKFLOW:
1. Prioritize requests based on injection potential
2. Start with automated scanning using sqli_scanner_tool
3. For any promising results, use manual testing with execute_python_code
4. Apply successful techniques to similar requests
5. Verify and exploit any discovered vulnerabilities
6. Generate proof-of-concept for each finding

Remember: You must return your findings as a JSON array following the exact schema specified in your expected_output. Include detailed evidence and curl commands for each vulnerability found.
"""

    def _parse_json_vulnerabilities(self, agent_response: str) -> List[Dict[str, Any]]:
        """Parse vulnerabilities from JSON response"""
        try:
            # Try to extract JSON from the response
            import json
            import re

            # Look for JSON array in the response
            json_pattern = r'\[[\s\S]*?\]'
            json_matches = re.findall(json_pattern, agent_response)

            if json_matches:
                # Try to parse the first JSON array found
                json_str = json_matches[0]
                vulnerabilities = json.loads(json_str)

                # Validate that it's a list
                if isinstance(vulnerabilities, list):
                    self.logger.info(f"Successfully parsed {len(vulnerabilities)} vulnerabilities from JSON response")
                    return vulnerabilities
                else:
                    self.logger.warning("JSON response is not a list, treating as single vulnerability")
                    return [vulnerabilities] if vulnerabilities else []
            else:
                # Fallback: try to parse the entire response as JSON
                vulnerabilities = json.loads(agent_response.strip())
                if isinstance(vulnerabilities, list):
                    return vulnerabilities
                else:
                    return [vulnerabilities] if vulnerabilities else []

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON response: {e}")
            self.logger.debug(f"Agent response content: {agent_response[:500]}...")

            # Fallback to legacy parsing if JSON parsing fails
            return self._fallback_parse_vulnerabilities(agent_response)
        except Exception as e:
            self.logger.error(f"Unexpected error parsing vulnerabilities: {e}")
            return []

    def _fallback_parse_vulnerabilities(self, agent_response: str) -> List[Dict[str, Any]]:
        """Fallback parsing method for non-JSON responses"""
        vulnerabilities = []

        # Look for vulnerability indicators in agent response
        if "vulnerable" in agent_response.lower() or "injection" in agent_response.lower():
            # Try to extract curl command and evidence using simple patterns
            curl_command = self._extract_curl_command(agent_response)
            evidence = self._extract_evidence(agent_response)

            vulnerability = {
                'type': 'SQL Injection',
                'parameter': 'N/A',
                'payload': 'N/A',
                'technique': 'Unknown',
                'curl_command': curl_command or 'N/A',
                'evidence': evidence or 'Vulnerability detected in agent response',
                'reasoning': 'Parsed from non-JSON agent response'
            }
            vulnerabilities.append(vulnerability)

        return vulnerabilities



    def _form_to_raw_request(self, form_info: Dict[str, Any]) -> str:
        """Convert form information to raw HTTP request"""
        method = form_info.get('method', 'GET').upper()
        action = form_info.get('action', '/')
        target_url = form_info.get('target_url', '')
        inputs = form_info.get('inputs', [])

        # Build URL
        if action.startswith('http'):
            url = action
        elif action.startswith('/'):
            base_url = target_url.rstrip('/')
            url = base_url + action
        else:
            base_url = target_url.rstrip('/')
            url = base_url + '/' + action

        # Build parameters
        params = []
        for input_field in inputs:
            name = input_field.get('name', '')
            value = input_field.get('value', 'test')
            if name:
                params.append(f"{name}={value}")

        param_string = '&'.join(params)

        if method == 'GET':
            if param_string:
                url += '?' + param_string
            return f"GET {url} HTTP/1.1\r\nHost: {target_url.split('//')[1].split('/')[0]}\r\nUser-Agent: Mozilla/5.0\r\n\r\n"
        else:
            return f"POST {url} HTTP/1.1\r\nHost: {target_url.split('//')[1].split('/')[0]}\r\nContent-Type: application/x-www-form-urlencoded\r\nContent-Length: {len(param_string)}\r\nUser-Agent: Mozilla/5.0\r\n\r\n{param_string}"

    def _network_request_to_raw(self, request: Dict[str, Any]) -> str:
        """Convert network request to raw HTTP request"""
        method = request.get('method', 'GET').upper()
        url = request.get('url', '')
        headers = request.get('headers', {})
        body = request.get('body', '')

        # Parse URL
        from urllib.parse import urlparse
        parsed = urlparse(url)
        host = parsed.netloc
        path = parsed.path + ('?' + parsed.query if parsed.query else '')

        # Build raw request
        raw_request = f"{method} {path} HTTP/1.1\r\n"
        raw_request += f"Host: {host}\r\n"

        # Add headers
        for header_name, header_value in headers.items():
            if header_name.lower() not in ['host', 'content-length']:
                raw_request += f"{header_name}: {header_value}\r\n"

        # Add body if present
        if body:
            raw_request += f"Content-Length: {len(body)}\r\n"
            raw_request += "\r\n"
            raw_request += body
        else:
            raw_request += "\r\n"

        return raw_request



    def _extract_curl_command(self, text: str) -> Optional[str]:
        """Extract curl command from text"""
        import re
        curl_pattern = r'curl[^"]*(?:"[^"]*"[^"]*)*'
        matches = re.findall(curl_pattern, text, re.IGNORECASE | re.MULTILINE)
        return matches[0] if matches else None

    def _extract_evidence(self, text: str) -> Optional[str]:
        """Extract evidence from text"""
        # Look for common evidence patterns
        evidence_patterns = [
            r'evidence["\']?\s*:\s*["\']([^"\']+)["\']',
            r'proof["\']?\s*:\s*["\']([^"\']+)["\']',
            r'vulnerable.*?:\s*([^\n]+)',
            r'injection.*?:\s*([^\n]+)'
        ]

        import re
        for pattern in evidence_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                return matches[0]

        return None


    
    def _save_sqli_report(self, results: Dict[str, Any], target_url: str) -> str:
        """Save SQLi scan results to file with LLM session summary"""
        try:
            # Add LLM session summary to results
            llm_session_summary = self.llm_logger.get_session_summary()
            results['llm_session_summary'] = llm_session_summary

            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            domain = target_url.replace("https://", "").replace("http://", "").replace("/", "_")
            filename = f"sqli_report_{domain}_{timestamp}.json"

            # Ensure reports directory exists
            reports_dir = Path(self.config.reports_dir)
            reports_dir.mkdir(parents=True, exist_ok=True)

            # Save report
            report_path = reports_dir / filename
            with open(report_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)

            self.logger.info(f"SQLi report saved to: {report_path}")
            self.logger.info(f"LLM session summary: {llm_session_summary}")
            return str(report_path)

        except Exception as e:
            self.logger.error(f"Error saving SQLi report: {e}")
            return ""

    def get_llm_session_summary(self) -> Dict[str, Any]:
        """Get LLM session summary for this agent"""
        return self.llm_logger.get_session_summary()