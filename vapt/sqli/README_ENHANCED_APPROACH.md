# Enhanced SQLi Agent - Grounded Vulnerability Detection

## Overview

This enhanced SQLi agent addresses the critical issue of **LLM hallucination** in vulnerability detection by replacing generic tools with specialized, self-validating functions that provide deterministic, structured output.

## The Problem: LLM Hallucination in SQLi Detection

The original approach using `execute_python_code` allowed the LLM too much freedom to fabricate evidence:

### Common Hallucination Patterns:
- **Time-Based SQLi**: LLM could hallucinate time delays that didn't exist
- **Error-Based SQLi**: LLM could invent database error messages not present in responses  
- **UNION-Based SQLi**: LLM could claim successful data retrieval without evidence
- **Boolean-Based SQLi**: LLM could fabricate differences between TRUE/FALSE responses

### Root Cause:
The LLM operated on its internal "world model" instead of being strictly constrained by real-world tool output.

## The Solution: Specialized, Self-Validating Tools

### New Tool Architecture:

#### 1. `test_time_based_sqli()`
- **Function**: Measures and compares baseline vs. payload response times
- **Returns**: Structured JSON with mathematical validation
- **Grounding**: Only reports vulnerability if delay >= 80% of expected time
- **Example Output**:
```json
{
  "is_vulnerable": true,
  "baseline_duration_sec": 0.234,
  "payload_duration_sec": 5.187,
  "time_difference_sec": 4.953,
  "conclusion": "Time-based SQLi CONFIRMED: Payload caused 4.95s delay"
}
```

#### 2. `test_error_based_sqli()`
- **Function**: Scans HTTP responses for database error patterns using regex
- **Returns**: Structured JSON with pattern matching results
- **Grounding**: Only reports vulnerability if specific error pattern is found
- **Example Output**:
```json
{
  "is_vulnerable": true,
  "matched_pattern": "You have an error in your SQL syntax",
  "response_snippet": "...You have an error in your SQL syntax...",
  "conclusion": "Error-based SQLi CONFIRMED: Found database error"
}
```

#### 3. `test_union_based_sqli()`
- **Function**: Systematically tests column counts for UNION injection
- **Returns**: Structured JSON with column count determination
- **Grounding**: Only reports vulnerability after finding correct column count
- **Example Output**:
```json
{
  "is_vulnerable": true,
  "correct_column_count": 3,
  "payload_used": "' UNION SELECT NULL,NULL,NULL--",
  "conclusion": "UNION-based SQLi CONFIRMED: Found correct column count of 3"
}
```

#### 4. `test_boolean_based_sqli()`
- **Function**: Compares responses from TRUE vs FALSE SQL conditions
- **Returns**: Structured JSON with content comparison results
- **Grounding**: Only reports vulnerability if responses actually differ
- **Example Output**:
```json
{
  "is_vulnerable": true,
  "content_is_different": true,
  "response_true_len": 1234,
  "response_false_len": 987,
  "conclusion": "Boolean-based SQLi CONFIRMED: Different responses detected"
}
```

## Enhanced System Prompt

The new system prompt enforces strict tool usage:

```
**YOUR PRIMARY DIRECTIVE: USE THE RIGHT TOOL AND TRUST ITS OUTPUT**
Your ONLY method for testing is to use the provided tools. The 'conclusion' 
field in the tool's JSON output is the source of truth. You MUST NOT report 
a vulnerability unless a tool explicitly confirms it with "is_vulnerable": true.
```

### Key Directives:
1. **Trust Tool Output**: If tool returns `"is_vulnerable": false`, do NOT claim vulnerability
2. **Use Appropriate Tools**: Match tool to SQLi hypothesis (error-based, time-based, etc.)
3. **Restrict Generic Tools**: Only use `execute_python_code` for exploitation AFTER confirmation
4. **Evidence-Based Reporting**: Construct reports using tool-provided evidence fields

## Benefits of the Enhanced Approach

### 1. Eliminates Hallucination
- **Before**: LLM could invent evidence to satisfy its goal
- **After**: LLM constrained by deterministic tool output

### 2. Structured Evidence
- **Before**: Fuzzy interpretation of raw HTTP responses
- **After**: Clear boolean flags and measured values

### 3. Separation of Concerns
- **Domain Logic**: Encapsulated in Python functions (timing, regex, comparison)
- **Strategy Logic**: LLM handles target selection and technique choice

### 4. Reproducible Results
- **Before**: Same input could produce different vulnerability claims
- **After**: Deterministic output based on actual measurements

## Usage Example

```python
from vapt.sqli.sqli_agent import SQLiAgent

# Initialize enhanced agent
agent = SQLiAgent()

# Agent will now use specialized tools:
# 1. Analyze batch of requests
# 2. Form hypothesis about SQLi type
# 3. Call appropriate specialized tool
# 4. Report only tool-confirmed vulnerabilities
```

## Implementation Files

- `vapt/sqli/sqli_tools.py` - Specialized testing tools
- `vapt/sqli/sqli_agent.py` - Enhanced agent with new system prompt
- `demo_enhanced_sqli_approach.py` - Demonstration script

## Testing

Run the demonstration to see the enhanced approach:

```bash
python demo_enhanced_sqli_approach.py
```

This will show example tool outputs and explain the grounding improvements.

## Impact

This enhanced approach should dramatically reduce false positives while maintaining comprehensive SQLi detection capabilities. The LLM can still apply strategic reasoning about target prioritization and technique selection, but is now grounded by reliable, validating tools that prevent fabrication of evidence.
