2025-07-30 00:37:39,886 - browser_agent - INFO - <PERSON><PERSON> logging initialized successfully
2025-07-30 00:37:39,887 - browser_agent - INFO - Browser Agent initialized for intelligent single-page deep analysis.
2025-07-30 00:37:39,888 - browser_agent - INFO - Starting deep analysis for single page: https://juice-shop.herokuapp.com
2025-07-30 00:37:39,888 - browser_agent - INFO - Connecting to MCP server...
2025-07-30 00:37:39,986 - browser_agent - INFO - Connected to MCP server successfully
2025-07-30 00:37:39,986 - browser_agent - INFO - Issuing deep analysis task order for: https://juice-shop.herokuapp.com
2025-07-30 00:37:39,986 - browser_agent - INFO - 🔄 Real-time data collection enabled - network and console data will be captured after each interaction
2025-07-30 00:37:52,066 - browser_agent - INFO - 📊 Real-time data collection summary: 0 network logs, 0 console logs, 0 raw requests collected
2025-07-30 00:37:52,066 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-30 00:37:57,034 - browser_agent - INFO - Browser cleanup command issued successfully.
2025-07-30 00:37:57,035 - browser_agent - INFO - Exhaustive analysis completed. App type: ecommerce
2025-07-30 00:37:58,038 - browser_agent - INFO - LLM Usage Summary: 1 interactions, $0.010938 cost, 111 input tokens, 1066 output tokens
2025-07-30 00:37:58,041 - browser_agent - INFO - Analysis complete. Report saved to: reports/recon_report_juice-shop.herokuapp.com_20250730_003758.json
