2025-07-30 01:10:41,101 - browser_agent - INFO - <PERSON><PERSON> logging initialized successfully
2025-07-30 01:10:41,101 - browser_agent - INFO - Browser Agent initialized for intelligent single-page deep analysis.
2025-07-30 01:10:41,102 - browser_agent - INFO - Starting deep analysis for single page: https://httpbin.org/html
2025-07-30 01:10:41,102 - browser_agent - INFO - Connecting to MCP server...
2025-07-30 01:10:41,179 - browser_agent - INFO - Connected to MCP server successfully
2025-07-30 01:10:41,179 - browser_agent - INFO - 🔍 Available MCP methods: ['initialize', 'register']
2025-07-30 01:10:41,179 - browser_agent - INFO - Issuing deep analysis task order for: https://httpbin.org/html
2025-07-30 01:10:41,179 - browser_agent - INFO - 🔄 Real-time data collection enabled - network and console data will be captured after each interaction
2025-07-30 01:10:43,772 - browser_agent - ERROR - ❌ No navigate method found. Available: []
2025-07-30 01:10:47,017 - browser_agent - ERROR - ❌ No navigate method found. Available: []
2025-07-30 01:10:48,779 - browser_agent - INFO - Attempting to collect final network and console data from Playwright MCP...
2025-07-30 01:10:52,556 - browser_agent - ERROR - ❌ No navigate method found. Available: []
2025-07-30 01:10:54,299 - browser_agent - ERROR - Failed to collect Playwright data: cannot access local variable 're' where it is not associated with a value
2025-07-30 01:10:54,300 - browser_agent - INFO - 📊 Final data collection summary: 0 network logs, 0 console logs, 0 raw requests collected
2025-07-30 01:10:54,300 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-30 01:10:55,603 - browser_agent - ERROR - ❌ No navigate method found. Available: []
2025-07-30 01:10:58,132 - browser_agent - ERROR - ❌ No click method found. Available: []
2025-07-30 01:11:01,992 - browser_agent - INFO - Browser cleanup command issued successfully.
2025-07-30 01:11:01,992 - browser_agent - INFO - Exhaustive analysis completed. App type: unknown
2025-07-30 01:11:02,995 - browser_agent - INFO - LLM Usage Summary: 3 interactions, $0.003735 cost, 514 input tokens, 245 output tokens
2025-07-30 01:11:02,997 - browser_agent - INFO - Analysis complete. Report saved to: reports/recon_report_httpbin.org_20250730_011102.json
