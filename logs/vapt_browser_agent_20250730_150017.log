2025-07-30 15:00:17,789 - browser_agent - INFO - <PERSON><PERSON> logging initialized successfully
2025-07-30 15:00:17,789 - browser_agent - INFO - Comprehensive Browser Agent initialized for exhaustive web crawling.
2025-07-30 15:00:17,790 - browser_agent - INFO - 🚀 Starting comprehensive web crawling for: https://brokencrystals.com/
2025-07-30 15:00:17,790 - browser_agent - INFO - Connecting to MCP server...
2025-07-30 15:00:17,893 - browser_agent - INFO - Connected to MCP server successfully
2025-07-30 15:00:17,893 - browser_agent - INFO - 🔍 Starting comprehensive single-page analysis of: https://brokencrystals.com/
2025-07-30 15:00:17,893 - browser_agent - INFO - 🔄 Executing single-page analysis phase: initial_navigation
2025-07-30 15:00:34,547 - browser_agent - INFO - ✅ Single-page phase initial_navigation completed successfully
2025-07-30 15:00:34,547 - browser_agent - INFO - 🔄 Executing single-page analysis phase: element_interaction
2025-07-30 15:00:47,433 - browser_agent - INFO - ✅ Single-page phase element_interaction completed successfully
2025-07-30 15:00:47,433 - browser_agent - INFO - 🔄 Executing single-page analysis phase: form_testing
2025-07-30 15:01:29,406 - browser_agent - INFO - ✅ Single-page phase form_testing completed successfully
2025-07-30 15:01:29,406 - browser_agent - INFO - 🔄 Executing single-page analysis phase: scroll_hover
2025-07-30 15:01:52,859 - browser_agent - INFO - ✅ Single-page phase scroll_hover completed successfully
2025-07-30 15:01:52,859 - browser_agent - INFO - 🔄 Executing single-page analysis phase: final_collection
2025-07-30 15:02:31,222 - browser_agent - INFO - ✅ Single-page phase final_collection completed successfully
2025-07-30 15:02:31,223 - browser_agent - INFO - 🌐 Collecting comprehensive network data...
2025-07-30 15:05:04,580 - browser_agent - ERROR - Agent execution failed on attempt 1: Requests to the ChatCompletions_Create Operation under Azure OpenAI API version 2024-12-01-preview have exceeded token rate limit of your current OpenAI S0 pricing tier. Please retry after 60 seconds. Please go here: https://aka.ms/oai/quotaincrease if you would like to further increase the default rate limit. For Free Account customers, upgrade to Pay as you Go here: https://aka.ms/429TrialUpgrade.
2025-07-30 15:05:31,364 - browser_agent - ERROR - Agent execution failed on attempt 2: This model's maximum context length is 128000 tokens. However, your messages resulted in 243213 tokens (242032 in the messages, 1181 in the functions). Please reduce the length of the messages or functions.
2025-07-30 15:07:58,678 - browser_agent - ERROR - Agent execution failed on attempt 3: Requests to the ChatCompletions_Create Operation under Azure OpenAI API version 2024-12-01-preview have exceeded token rate limit of your current OpenAI S0 pricing tier. Please retry after 60 seconds. Please go here: https://aka.ms/oai/quotaincrease if you would like to further increase the default rate limit. For Free Account customers, upgrade to Pay as you Go here: https://aka.ms/429TrialUpgrade.
2025-07-30 15:07:58,678 - browser_agent - ERROR - ❌ Failed to collect network data: Requests to the ChatCompletions_Create Operation under Azure OpenAI API version 2024-12-01-preview have exceeded token rate limit of your current OpenAI S0 pricing tier. Please retry after 60 seconds. Please go here: https://aka.ms/oai/quotaincrease if you would like to further increase the default rate limit. For Free Account customers, upgrade to Pay as you Go here: https://aka.ms/429TrialUpgrade.
2025-07-30 15:07:58,678 - browser_agent - INFO - 📝 Collecting comprehensive console data...
2025-07-30 15:08:03,701 - browser_agent - INFO - 📊 Enhanced console parsing found 4 console messages
2025-07-30 15:08:03,702 - browser_agent - INFO - 📊 Collected 4 console messages
2025-07-30 15:08:03,702 - browser_agent - INFO - 📊 Crawling Statistics:
2025-07-30 15:08:03,702 - browser_agent - INFO -    - Total interactions: 84
2025-07-30 15:08:03,702 - browser_agent - INFO -    - Elements discovered: 0
2025-07-30 15:08:03,702 - browser_agent - INFO -    - Elements interacted: 0
2025-07-30 15:08:03,702 - browser_agent - INFO -    - Network requests: 0
2025-07-30 15:08:03,702 - browser_agent - INFO -    - Console messages: 4
2025-07-30 15:08:03,702 - browser_agent - INFO - 🧹 Ensuring browser cleanup...
2025-07-30 15:08:06,469 - browser_agent - INFO - ✅ Browser cleanup command issued successfully.
2025-07-30 15:08:06,469 - browser_agent - INFO - ✅ Comprehensive single-page analysis completed successfully
2025-07-30 15:08:08,471 - browser_agent - INFO - 💰 LLM Usage Summary:
2025-07-30 15:08:08,471 - browser_agent - INFO -    - Interactions: 9
2025-07-30 15:08:08,471 - browser_agent - INFO -    - Cost: $0.024398
2025-07-30 15:08:08,471 - browser_agent - INFO -    - Input tokens: 1531
2025-07-30 15:08:08,471 - browser_agent - INFO -    - Output tokens: 2057
2025-07-30 15:08:08,472 - browser_agent - INFO - 🎯 Application Analysis: social/community (confidence: 0.67)
2025-07-30 15:08:08,476 - browser_agent - INFO - ✅ Comprehensive crawling complete. Report saved to: reports/recon_report_brokencrystals.com_20250730_150808.json
