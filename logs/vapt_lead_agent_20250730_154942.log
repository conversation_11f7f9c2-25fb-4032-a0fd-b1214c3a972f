2025-07-30 15:49:42,604 - lead_agent - INFO - Lead Agent initialized with LLM logging
2025-07-30 15:49:42,606 - lead_agent - INFO - All component agents initialized successfully
2025-07-30 15:49:42,606 - lead_agent - INFO - Starting vulnerability scan for types: ['sqli']
2025-07-30 15:49:42,607 - lead_agent - INFO - Running intelligent SQLi vulnerability scan
2025-07-30 15:49:42,607 - lead_agent - INFO - SQLi Target Assessment Summary:
2025-07-30 15:49:42,607 - lead_agent - INFO -   Total requests: 14
2025-07-30 15:49:42,607 - lead_agent - INFO -   Testable requests: 6
2025-07-30 15:49:42,607 - lead_agent - INFO -   High priority: 4
2025-07-30 15:49:42,607 - lead_agent - INFO -   Medium priority: 2
2025-07-30 15:49:42,607 - lead_agent - INFO -   Low priority: 0
2025-07-30 15:49:42,608 - lead_agent - INFO - Found 6 promising SQLi targets
2025-07-30 15:49:42,608 - lead_agent - INFO -   Target 1: GET https://brokencrystals.com/api/users/one/undefined/adminpermission
2025-07-30 15:49:42,608 - lead_agent - INFO -     Priority: 3, Confidence: 0.80
2025-07-30 15:49:42,608 - lead_agent - INFO -     Reasons: High-risk endpoint: /api/user, High-risk endpoint: /api/users, High-risk endpoint: /user, High-risk endpoint: /admin, Client error response (may indicate input validation)
2025-07-30 15:49:42,608 - lead_agent - INFO -   Target 2: GET https://brokencrystals.com/api/users/one/undefined/photo
2025-07-30 15:49:42,608 - lead_agent - INFO -     Priority: 3, Confidence: 0.80
2025-07-30 15:49:42,608 - lead_agent - INFO -     Reasons: High-risk endpoint: /api/user, High-risk endpoint: /api/users, High-risk endpoint: /user, Client error response (may indicate input validation)
2025-07-30 15:49:42,608 - lead_agent - INFO -   Target 3: POST https://brokencrystals.com/api/metadata
2025-07-30 15:49:42,608 - lead_agent - INFO -     Priority: 3, Confidence: 0.80
2025-07-30 15:49:42,608 - lead_agent - INFO -     Reasons: API/query endpoint: /api/, HTTP POST method (accepts data)
2025-07-30 15:49:42,608 - lead_agent - INFO -   Target 4: GET https://brokencrystals.com/api/users/one/undefined/adminpermission
2025-07-30 15:49:42,608 - lead_agent - INFO -     Priority: 3, Confidence: 0.80
2025-07-30 15:49:42,608 - lead_agent - INFO -     Reasons: High-risk endpoint: /api/user, High-risk endpoint: /api/users, High-risk endpoint: /user, High-risk endpoint: /admin, Client error response (may indicate input validation)
2025-07-30 15:49:42,608 - lead_agent - INFO -   Target 5: GET https://brokencrystals.com/api/spawn?command=pwd
2025-07-30 15:49:42,608 - lead_agent - INFO -     Priority: 2, Confidence: 0.70
2025-07-30 15:49:42,608 - lead_agent - INFO -     Reasons: API/query endpoint: /api/, URL contains query parameters
2025-07-30 15:49:42,608 - lead_agent - INFO -   ... and 1 more targets
2025-07-30 15:54:58,753 - lead_agent - INFO - Lead agent coordination performance: {'agent_name': 'lead_agent', 'operation_type': 'vulnerability_coordination', 'duration_seconds': 316.14657497406006, 'success': True, 'timestamp': '2025-07-30T15:54:58.753288', 'vulnerability_types_scanned': ['sqli'], 'sqli_targets_found': 0, 'xss_targets_found': 0, 'total_scan_duration': 316.14657497406006}
2025-07-30 15:54:58,754 - lead_agent - INFO - VAPT results saved to: reports/vapt_results_https_brokencrystals.com__20250730_155458.json
2025-07-30 15:54:58,754 - lead_agent - INFO - Lead agent LLM session summary: {'agent_name': 'lead_agent', 'agent_type': 'coordination_agent', 'total_interactions': 1, 'session_id': 'b910d0de-a972-460f-9244-b5b94ef51e18', 'session_start': '2025-07-30T15:49:42.604181', 'total_cost_usd': 0.0025375000000000003, 'total_input_tokens': 1015, 'total_output_tokens': 0, 'session_duration': '0:05:16.149362', 'average_cost_per_interaction': 0.0025375000000000003}
2025-07-30 15:54:58,754 - lead_agent - INFO - Vulnerability scan completed. Results saved to: reports/vapt_results_https_brokencrystals.com__20250730_155458.json
