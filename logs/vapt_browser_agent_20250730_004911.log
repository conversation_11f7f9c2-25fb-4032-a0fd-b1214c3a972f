2025-07-30 00:49:11,220 - browser_agent - <PERSON>FO - <PERSON><PERSON> logging initialized successfully
2025-07-30 00:49:11,220 - browser_agent - INFO - Browser Agent initialized for intelligent single-page deep analysis.
2025-07-30 00:49:11,221 - browser_agent - INFO - Starting deep analysis for single page: https://www.hackthissite.org/
2025-07-30 00:49:11,221 - browser_agent - <PERSON><PERSON>O - Connecting to MCP server...
2025-07-30 00:49:11,281 - browser_agent - <PERSON><PERSON>O - Connected to MCP server successfully
2025-07-30 00:49:11,281 - browser_agent - INFO - Issuing deep analysis task order for: https://www.hackthissite.org/
2025-07-30 00:49:13,545 - browser_agent - ERROR - Agent execution failed on attempt 1: The response was filtered due to the prompt triggering Azure OpenAI's content management policy. Please modify your prompt and retry. To learn more about our content filtering policies please read our documentation: https://go.microsoft.com/fwlink/?linkid=2198766
2025-07-30 00:49:21,236 - browser_agent - ERROR - Agent execution failed on attempt 2: The response was filtered due to the prompt triggering Azure OpenAI's content management policy. Please modify your prompt and retry. To learn more about our content filtering policies please read our documentation: https://go.microsoft.com/fwlink/?linkid=2198766
2025-07-30 00:49:28,334 - browser_agent - ERROR - Agent execution failed on attempt 3: The response was filtered due to the prompt triggering Azure OpenAI's content management policy. Please modify your prompt and retry. To learn more about our content filtering policies please read our documentation: https://go.microsoft.com/fwlink/?linkid=2198766
2025-07-30 00:49:28,334 - browser_agent - ERROR - Agent Error [browser_agent] in analysis execution: The response was filtered due to the prompt triggering Azure OpenAI's content management policy. Please modify your prompt and retry. To learn more about our content filtering policies please read our documentation: https://go.microsoft.com/fwlink/?linkid=2198766
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 390, in ainvoke
    return await self.get_async_client().chat.completions.create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/resources/chat/completions/completions.py", line 2454, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<45 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/_base_client.py", line 1791, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/_base_client.py", line 1591, in request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'error': {'message': "The response was filtered due to the prompt triggering Azure OpenAI's content management policy. Please modify your prompt and retry. To learn more about our content filtering policies please read our documentation: https://go.microsoft.com/fwlink/?linkid=2198766", 'type': None, 'param': 'prompt', 'code': 'content_filter', 'status': 400, 'innererror': {'code': 'ResponsibleAIPolicyViolation', 'content_filter_result': {'hate': {'filtered': False, 'severity': 'safe'}, 'jailbreak': {'filtered': True, 'detected': True}, 'self_harm': {'filtered': False, 'severity': 'safe'}, 'sexual': {'filtered': False, 'severity': 'safe'}, 'violence': {'filtered': False, 'severity': 'safe'}}}}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 365, in _perform_reconnaissance
    analysis_results = await self._execute_with_retry(agent, analysis_prompt)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 563, in _execute_with_retry
    raise e
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 528, in _execute_with_retry
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1554, in arun
    raise last_exception
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1511, in arun
    response = await self._arun(
               ^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1213, in _arun
    model_response: ModelResponse = await self.model.aresponse(
                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 449, in aresponse
    await self._aprocess_model_response(
    ...<6 lines>...
    )
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 609, in _aprocess_model_response
    response = await self.ainvoke(
               ^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 423, in ainvoke
    raise ModelProviderError(
    ...<4 lines>...
    ) from e
agno.exceptions.ModelProviderError: The response was filtered due to the prompt triggering Azure OpenAI's content management policy. Please modify your prompt and retry. To learn more about our content filtering policies please read our documentation: https://go.microsoft.com/fwlink/?linkid=2198766
2025-07-30 00:49:28,348 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-30 00:49:31,093 - browser_agent - INFO - Browser cleanup command issued successfully.
2025-07-30 00:49:33,149 - browser_agent - ERROR - Failed to connect to MCP server: generator didn't stop after athrow()
2025-07-30 00:49:33,149 - browser_agent - INFO - Make sure MCP server is running at: http://localhost:8931
2025-07-30 00:49:33,149 - browser_agent - INFO - Start it with: node cli.js --browser chrome --port 8931
2025-07-30 00:49:33,149 - browser_agent - ERROR - Agent Error [browser_agent] in reconnaissance: generator didn't stop after athrow()
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 390, in ainvoke
    return await self.get_async_client().chat.completions.create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/resources/chat/completions/completions.py", line 2454, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<45 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/_base_client.py", line 1791, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/_base_client.py", line 1591, in request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'error': {'message': "The response was filtered due to the prompt triggering Azure OpenAI's content management policy. Please modify your prompt and retry. To learn more about our content filtering policies please read our documentation: https://go.microsoft.com/fwlink/?linkid=2198766", 'type': None, 'param': 'prompt', 'code': 'content_filter', 'status': 400, 'innererror': {'code': 'ResponsibleAIPolicyViolation', 'content_filter_result': {'hate': {'filtered': False, 'severity': 'safe'}, 'jailbreak': {'filtered': True, 'detected': True}, 'self_harm': {'filtered': False, 'severity': 'safe'}, 'sexual': {'filtered': False, 'severity': 'safe'}, 'violence': {'filtered': False, 'severity': 'safe'}}}}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/shared/mcp_manager.py", line 196, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 170, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 215, in run_reconnaissance
    await self._perform_reconnaissance(agent, target_url)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 365, in _perform_reconnaissance
    analysis_results = await self._execute_with_retry(agent, analysis_prompt)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 563, in _execute_with_retry
    raise e
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 528, in _execute_with_retry
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1554, in arun
    raise last_exception
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1511, in arun
    response = await self._arun(
               ^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1213, in _arun
    model_response: ModelResponse = await self.model.aresponse(
                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 449, in aresponse
    await self._aprocess_model_response(
    ...<6 lines>...
    )
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 609, in _aprocess_model_response
    response = await self.ainvoke(
               ^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 423, in ainvoke
    raise ModelProviderError(
    ...<4 lines>...
    ) from e
agno.exceptions.ModelProviderError: The response was filtered due to the prompt triggering Azure OpenAI's content management policy. Please modify your prompt and retry. To learn more about our content filtering policies please read our documentation: https://go.microsoft.com/fwlink/?linkid=2198766

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 199, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 235, in __aexit__
    await self.gen.athrow(value)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 168, in _get_mcp_tools
    async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 271, in __aexit__
    raise RuntimeError("generator didn't stop after athrow()")
RuntimeError: generator didn't stop after athrow()
2025-07-30 00:49:33,154 - browser_agent - INFO - 🚨 Emergency report saved to: reports/recon_report_www.hackthissite.org_20250730_004933_partial.json
