2025-07-30 01:07:11,385 - browser_agent - INFO - <PERSON><PERSON> logging initialized successfully
2025-07-30 01:07:11,385 - browser_agent - INFO - Browser Agent initialized for intelligent single-page deep analysis.
2025-07-30 01:07:11,386 - browser_agent - INFO - Starting deep analysis for single page: https://httpbin.org/html
2025-07-30 01:07:11,386 - browser_agent - INFO - Connecting to MCP server...
2025-07-30 01:07:11,478 - browser_agent - INFO - Connected to MCP server successfully
2025-07-30 01:07:11,478 - browser_agent - INFO - Issuing deep analysis task order for: https://httpbin.org/html
2025-07-30 01:07:11,478 - browser_agent - INFO - 🔄 Real-time data collection enabled - network and console data will be captured after each interaction
2025-07-30 01:07:19,624 - browser_agent - INFO - Attempting to collect final network and console data from Playwright MCP...
2025-07-30 01:07:23,279 - browser_agent - ERROR - Failed to collect Playwright data: cannot access local variable 're' where it is not associated with a value
2025-07-30 01:07:23,279 - browser_agent - INFO - 📊 Final data collection summary: 0 network logs, 0 console logs, 0 raw requests collected
2025-07-30 01:07:23,279 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-30 01:07:32,488 - browser_agent - INFO - Browser cleanup command issued successfully.
2025-07-30 01:07:32,489 - browser_agent - INFO - Exhaustive analysis completed. App type: unknown
2025-07-30 01:07:33,493 - browser_agent - INFO - LLM Usage Summary: 3 interactions, $0.003615 cost, 514 input tokens, 233 output tokens
2025-07-30 01:07:33,495 - browser_agent - INFO - Analysis complete. Report saved to: reports/recon_report_httpbin.org_20250730_010733.json
