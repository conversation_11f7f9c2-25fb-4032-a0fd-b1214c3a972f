2025-07-30 00:49:11,218 - main - INFO - Starting VAPT scan for https://www.hackthissite.org/
2025-07-30 00:49:11,218 - main - INFO - Vulnerability types: sqli
2025-07-30 00:49:11,218 - main - INFO - Output directory: ./reports
2025-07-30 00:49:11,218 - main - INFO - Browser headless mode: False
2025-07-30 00:49:11,221 - main - INFO - Running full VAPT scan
2025-07-30 00:49:34,174 - main - ERROR - Scan execution failed: generator didn't stop after athrow()
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 390, in ainvoke
    return await self.get_async_client().chat.completions.create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/resources/chat/completions/completions.py", line 2454, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<45 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/_base_client.py", line 1791, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/_base_client.py", line 1591, in request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'error': {'message': "The response was filtered due to the prompt triggering Azure OpenAI's content management policy. Please modify your prompt and retry. To learn more about our content filtering policies please read our documentation: https://go.microsoft.com/fwlink/?linkid=2198766", 'type': None, 'param': 'prompt', 'code': 'content_filter', 'status': 400, 'innererror': {'code': 'ResponsibleAIPolicyViolation', 'content_filter_result': {'hate': {'filtered': False, 'severity': 'safe'}, 'jailbreak': {'filtered': True, 'detected': True}, 'self_harm': {'filtered': False, 'severity': 'safe'}, 'sexual': {'filtered': False, 'severity': 'safe'}, 'violence': {'filtered': False, 'severity': 'safe'}}}}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/shared/mcp_manager.py", line 196, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 170, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 215, in run_reconnaissance
    await self._perform_reconnaissance(agent, target_url)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 365, in _perform_reconnaissance
    analysis_results = await self._execute_with_retry(agent, analysis_prompt)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 563, in _execute_with_retry
    raise e
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 528, in _execute_with_retry
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1554, in arun
    raise last_exception
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1511, in arun
    response = await self._arun(
               ^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1213, in _arun
    model_response: ModelResponse = await self.model.aresponse(
                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 449, in aresponse
    await self._aprocess_model_response(
    ...<6 lines>...
    )
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 609, in _aprocess_model_response
    response = await self.ainvoke(
               ^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 423, in ainvoke
    raise ModelProviderError(
    ...<4 lines>...
    ) from e
agno.exceptions.ModelProviderError: The response was filtered due to the prompt triggering Azure OpenAI's content management policy. Please modify your prompt and retry. To learn more about our content filtering policies please read our documentation: https://go.microsoft.com/fwlink/?linkid=2198766

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/main.py", line 131, in _run_scan
    results = await lead_agent.run_full_scan(url, vuln_types)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/lead/lead_agent.py", line 381, in run_full_scan
    report_path = await self.run_preprocessor_only(target_url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/lead/lead_agent.py", line 244, in run_preprocessor_only
    report_path = await self.browser_agent.run_reconnaissance(target_url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 199, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 235, in __aexit__
    await self.gen.athrow(value)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 168, in _get_mcp_tools
    async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 271, in __aexit__
    raise RuntimeError("generator didn't stop after athrow()")
RuntimeError: generator didn't stop after athrow()
2025-07-30 00:49:34,187 - main - ERROR - Scan failed: generator didn't stop after athrow()
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 390, in ainvoke
    return await self.get_async_client().chat.completions.create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/resources/chat/completions/completions.py", line 2454, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<45 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/_base_client.py", line 1791, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/_base_client.py", line 1591, in request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'error': {'message': "The response was filtered due to the prompt triggering Azure OpenAI's content management policy. Please modify your prompt and retry. To learn more about our content filtering policies please read our documentation: https://go.microsoft.com/fwlink/?linkid=2198766", 'type': None, 'param': 'prompt', 'code': 'content_filter', 'status': 400, 'innererror': {'code': 'ResponsibleAIPolicyViolation', 'content_filter_result': {'hate': {'filtered': False, 'severity': 'safe'}, 'jailbreak': {'filtered': True, 'detected': True}, 'self_harm': {'filtered': False, 'severity': 'safe'}, 'sexual': {'filtered': False, 'severity': 'safe'}, 'violence': {'filtered': False, 'severity': 'safe'}}}}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/shared/mcp_manager.py", line 196, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 170, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 215, in run_reconnaissance
    await self._perform_reconnaissance(agent, target_url)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 365, in _perform_reconnaissance
    analysis_results = await self._execute_with_retry(agent, analysis_prompt)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 563, in _execute_with_retry
    raise e
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 528, in _execute_with_retry
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1554, in arun
    raise last_exception
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1511, in arun
    response = await self._arun(
               ^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1213, in _arun
    model_response: ModelResponse = await self.model.aresponse(
                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 449, in aresponse
    await self._aprocess_model_response(
    ...<6 lines>...
    )
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 609, in _aprocess_model_response
    response = await self.ainvoke(
               ^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 423, in ainvoke
    raise ModelProviderError(
    ...<4 lines>...
    ) from e
agno.exceptions.ModelProviderError: The response was filtered due to the prompt triggering Azure OpenAI's content management policy. Please modify your prompt and retry. To learn more about our content filtering policies please read our documentation: https://go.microsoft.com/fwlink/?linkid=2198766

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/main.py", line 80, in scan
    asyncio.run(_run_scan(url, vulns, report_name, config, logger))
    ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/main.py", line 131, in _run_scan
    results = await lead_agent.run_full_scan(url, vuln_types)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/lead/lead_agent.py", line 381, in run_full_scan
    report_path = await self.run_preprocessor_only(target_url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/lead/lead_agent.py", line 244, in run_preprocessor_only
    report_path = await self.browser_agent.run_reconnaissance(target_url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 199, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 235, in __aexit__
    await self.gen.athrow(value)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 168, in _get_mcp_tools
    async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 271, in __aexit__
    raise RuntimeError("generator didn't stop after athrow()")
RuntimeError: generator didn't stop after athrow()
