2025-07-30 14:03:27,807 - browser_agent - INFO - <PERSON><PERSON> logging initialized successfully
2025-07-30 14:03:27,807 - browser_agent - INFO - Comprehensive Browser Agent initialized for exhaustive web crawling.
2025-07-30 14:03:27,809 - browser_agent - INFO - 🚀 Starting comprehensive web crawling for: https://brokencrystals.com/
2025-07-30 14:03:27,809 - browser_agent - INFO - Connecting to MCP server...
2025-07-30 14:03:27,905 - browser_agent - INFO - Connected to MCP server successfully
2025-07-30 14:03:27,905 - browser_agent - INFO - 🔍 Starting comprehensive single-page analysis of: https://brokencrystals.com/
2025-07-30 14:03:27,905 - browser_agent - INFO - 🔄 Executing single-page analysis phase: initial_navigation
2025-07-30 14:03:45,552 - browser_agent - INFO - ✅ Single-page phase initial_navigation completed successfully
2025-07-30 14:03:45,552 - browser_agent - INFO - 🔄 Executing single-page analysis phase: element_interaction
2025-07-30 14:03:53,650 - browser_agent - INFO - ✅ Single-page phase element_interaction completed successfully
2025-07-30 14:03:53,650 - browser_agent - INFO - 🔄 Executing single-page analysis phase: form_testing
2025-07-30 14:04:17,150 - browser_agent - INFO - ✅ Single-page phase form_testing completed successfully
2025-07-30 14:04:17,150 - browser_agent - INFO - 🔄 Executing single-page analysis phase: scroll_hover
2025-07-30 14:04:30,898 - browser_agent - INFO - ✅ Single-page phase scroll_hover completed successfully
2025-07-30 14:04:30,898 - browser_agent - INFO - 🔄 Executing single-page analysis phase: final_collection
2025-07-30 14:05:11,530 - browser_agent - INFO - ✅ Single-page phase final_collection completed successfully
2025-07-30 14:05:11,530 - browser_agent - INFO - 🌐 Collecting comprehensive network data...
2025-07-30 14:05:58,432 - browser_agent - ERROR - Agent execution failed on attempt 1: This model's maximum context length is 128000 tokens. However, your messages resulted in 256768 tokens (255587 in the messages, 1181 in the functions). Please reduce the length of the messages or functions.
2025-07-30 14:07:05,593 - browser_agent - ERROR - Agent execution failed on attempt 2: This model's maximum context length is 128000 tokens. However, your messages resulted in 253634 tokens (252453 in the messages, 1181 in the functions). Please reduce the length of the messages or functions.
2025-07-30 14:08:13,481 - browser_agent - ERROR - Agent execution failed on attempt 3: This model's maximum context length is 128000 tokens. However, your messages resulted in 253634 tokens (252453 in the messages, 1181 in the functions). Please reduce the length of the messages or functions.
2025-07-30 14:08:13,482 - browser_agent - ERROR - ❌ Failed to collect network data: This model's maximum context length is 128000 tokens. However, your messages resulted in 253634 tokens (252453 in the messages, 1181 in the functions). Please reduce the length of the messages or functions.
2025-07-30 14:08:13,482 - browser_agent - INFO - 📝 Collecting comprehensive console data...
2025-07-30 14:08:18,431 - browser_agent - INFO - 📊 Parsed 15 console messages from MCP data
2025-07-30 14:08:18,431 - browser_agent - INFO - 📊 Collected 15 console messages
2025-07-30 14:08:18,431 - browser_agent - INFO - 📊 Crawling Statistics:
2025-07-30 14:08:18,431 - browser_agent - INFO -    - Total interactions: 83
2025-07-30 14:08:18,431 - browser_agent - INFO -    - Elements discovered: 0
2025-07-30 14:08:18,431 - browser_agent - INFO -    - Elements interacted: 0
2025-07-30 14:08:18,431 - browser_agent - INFO -    - Network requests: 0
2025-07-30 14:08:18,432 - browser_agent - INFO -    - Console messages: 15
2025-07-30 14:08:18,432 - browser_agent - INFO - 🧹 Ensuring browser cleanup...
2025-07-30 14:08:20,807 - browser_agent - INFO - ✅ Browser cleanup command issued successfully.
2025-07-30 14:08:20,807 - browser_agent - INFO - ✅ Comprehensive single-page analysis completed successfully
2025-07-30 14:08:22,811 - browser_agent - INFO - 💰 LLM Usage Summary:
2025-07-30 14:08:22,811 - browser_agent - INFO -    - Interactions: 9
2025-07-30 14:08:22,812 - browser_agent - INFO -    - Cost: $0.022720
2025-07-30 14:08:22,812 - browser_agent - INFO -    - Input tokens: 1504
2025-07-30 14:08:22,812 - browser_agent - INFO -    - Output tokens: 1896
2025-07-30 14:08:22,813 - browser_agent - INFO - 🎯 Application Analysis: api/service (confidence: 0.67)
2025-07-30 14:08:22,817 - browser_agent - INFO - ✅ Comprehensive crawling complete. Report saved to: reports/recon_report_brokencrystals.com_20250730_140822.json
