2025-07-30 00:54:27,178 - browser_agent - INFO - <PERSON><PERSON> logging initialized successfully
2025-07-30 00:54:27,178 - browser_agent - INFO - Browser Agent initialized for intelligent single-page deep analysis.
2025-07-30 00:54:27,179 - browser_agent - INFO - Starting deep analysis for single page: https://public-firing-range.appspot.com/
2025-07-30 00:54:27,179 - browser_agent - INFO - Connecting to MCP server...
2025-07-30 00:54:27,275 - browser_agent - INFO - Connected to MCP server successfully
2025-07-30 00:54:27,275 - browser_agent - INFO - Issuing deep analysis task order for: https://public-firing-range.appspot.com/
2025-07-30 00:55:33,271 - browser_agent - INFO - Collecting final network and console data from Playwright MCP...
2025-07-30 00:55:53,580 - browser_agent - ERROR - Failed to collect Playwright data: cannot access local variable 're' where it is not associated with a value
2025-07-30 00:55:53,581 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-30 00:55:56,075 - browser_agent - INFO - Browser cleanup command issued successfully.
2025-07-30 00:55:56,076 - browser_agent - INFO - Exhaustive analysis completed. App type: cms/blog
2025-07-30 00:55:57,080 - browser_agent - INFO - LLM Usage Summary: 3 interactions, $0.025150 cost, 320 input tokens, 2435 output tokens
2025-07-30 00:55:57,083 - browser_agent - INFO - Analysis complete. Report saved to: reports/recon_report_public-firing-range.appspot.com_20250730_005557.json
