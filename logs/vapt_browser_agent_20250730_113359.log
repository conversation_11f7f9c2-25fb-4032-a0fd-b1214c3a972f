2025-07-30 11:33:59,704 - browser_agent - INFO - <PERSON><PERSON> logging initialized successfully
2025-07-30 11:33:59,704 - browser_agent - INFO - Comprehensive Browser Agent initialized for exhaustive web crawling.
2025-07-30 11:33:59,705 - browser_agent - INFO - 🚀 Starting comprehensive web crawling for: https://juice-shop.herokuapp.com
2025-07-30 11:33:59,705 - browser_agent - INFO - Connecting to MCP server...
2025-07-30 11:33:59,830 - browser_agent - INFO - Connected to MCP server successfully
2025-07-30 11:33:59,830 - browser_agent - INFO - 🔍 Starting comprehensive crawling of: https://juice-shop.herokuapp.com
2025-07-30 11:33:59,830 - browser_agent - INFO - 🔄 Executing crawling phase: initial_navigation
2025-07-30 11:34:13,590 - browser_agent - INFO - ✅ Phase initial_navigation completed successfully
2025-07-30 11:34:13,590 - browser_agent - INFO - 🔄 Executing crawling phase: element_interaction
2025-07-30 11:35:04,077 - browser_agent - INFO - ✅ Phase element_interaction completed successfully
2025-07-30 11:35:04,077 - browser_agent - INFO - 🔄 Executing crawling phase: form_testing
2025-07-30 11:37:36,315 - browser_agent - INFO - ✅ Phase form_testing completed successfully
2025-07-30 11:37:36,315 - browser_agent - INFO - 🔄 Executing crawling phase: scroll_hover
2025-07-30 11:37:58,069 - browser_agent - INFO - ✅ Phase scroll_hover completed successfully
2025-07-30 11:37:58,070 - browser_agent - INFO - 🔄 Executing crawling phase: final_collection
2025-07-30 11:38:19,101 - browser_agent - INFO - ✅ Phase final_collection completed successfully
2025-07-30 11:38:19,101 - browser_agent - INFO - 🌐 Collecting comprehensive network data...
2025-07-30 11:41:08,419 - browser_agent - ERROR - Agent execution failed on attempt 1: Requests to the ChatCompletions_Create Operation under Azure OpenAI API version 2024-12-01-preview have exceeded token rate limit of your current OpenAI S0 pricing tier. Please retry after 60 seconds. Please go here: https://aka.ms/oai/quotaincrease if you would like to further increase the default rate limit. For Free Account customers, upgrade to Pay as you Go here: https://aka.ms/429TrialUpgrade.
2025-07-30 11:41:58,922 - browser_agent - WARNING - 🛑 Received termination signal 2
2025-07-30 11:42:33,294 - browser_agent - WARNING - 🛑 Received termination signal 2
2025-07-30 11:42:33,631 - browser_agent - WARNING - 🛑 Received termination signal 2
2025-07-30 11:42:33,856 - browser_agent - WARNING - 🛑 Received termination signal 2
2025-07-30 11:42:34,043 - browser_agent - WARNING - 🛑 Received termination signal 2
2025-07-30 11:42:34,195 - browser_agent - WARNING - 🛑 Received termination signal 2
2025-07-30 11:43:47,760 - browser_agent - WARNING - 🛑 Received termination signal 1
2025-07-30 11:43:47,766 - browser_agent - WARNING - 🛑 Received termination signal 1
2025-07-30 11:46:13,413 - browser_agent - INFO - 🛑 Handling termination: signal_interrupt
2025-07-30 11:46:13,415 - browser_agent - INFO - 🔄 Attempting to close browser before termination...
2025-07-30 11:46:13,416 - browser_agent - INFO - 🧹 Ensuring browser cleanup...
2025-07-30 11:46:13,455 - browser_agent - INFO - 🛑 Handling termination: signal_interrupt
2025-07-30 11:46:13,455 - browser_agent - INFO - 🔄 Attempting to close browser before termination...
2025-07-30 11:46:13,455 - browser_agent - INFO - 🧹 Ensuring browser cleanup...
2025-07-30 11:46:13,475 - browser_agent - INFO - 🛑 Handling termination: signal_interrupt
2025-07-30 11:46:13,475 - browser_agent - INFO - 🔄 Attempting to close browser before termination...
2025-07-30 11:46:13,476 - browser_agent - INFO - 🧹 Ensuring browser cleanup...
2025-07-30 11:46:13,494 - browser_agent - INFO - 🛑 Handling termination: signal_interrupt
2025-07-30 11:46:13,494 - browser_agent - INFO - 🔄 Attempting to close browser before termination...
2025-07-30 11:46:13,494 - browser_agent - INFO - 🧹 Ensuring browser cleanup...
2025-07-30 11:46:13,512 - browser_agent - INFO - 🛑 Handling termination: signal_interrupt
2025-07-30 11:46:13,513 - browser_agent - INFO - 🔄 Attempting to close browser before termination...
2025-07-30 11:46:13,513 - browser_agent - INFO - 🧹 Ensuring browser cleanup...
2025-07-30 11:46:13,532 - browser_agent - INFO - 🛑 Handling termination: signal_interrupt
2025-07-30 11:46:13,532 - browser_agent - INFO - 🔄 Attempting to close browser before termination...
2025-07-30 11:46:13,532 - browser_agent - INFO - 🧹 Ensuring browser cleanup...
2025-07-30 11:46:13,551 - browser_agent - INFO - 🛑 Handling termination: signal_interrupt
2025-07-30 11:46:13,551 - browser_agent - INFO - 🔄 Attempting to close browser before termination...
2025-07-30 11:46:13,551 - browser_agent - INFO - 🧹 Ensuring browser cleanup...
2025-07-30 11:46:13,568 - browser_agent - INFO - 🛑 Handling termination: signal_interrupt
2025-07-30 11:46:13,568 - browser_agent - INFO - 🔄 Attempting to close browser before termination...
2025-07-30 11:46:13,569 - browser_agent - INFO - 🧹 Ensuring browser cleanup...
2025-07-30 11:46:13,589 - browser_agent - WARNING - ⏰ Agent attempt 2 timed out after 5 minutes
2025-07-30 11:46:13,589 - browser_agent - INFO - 📊 Parsed 0 network requests from MCP data
2025-07-30 11:46:13,589 - browser_agent - INFO - 📊 Extracted 0 raw HTTP requests
2025-07-30 11:46:13,589 - browser_agent - INFO - 📊 Collected 0 network requests
2025-07-30 11:46:13,589 - browser_agent - INFO - 📝 Collecting comprehensive console data...
2025-07-30 11:46:13,590 - browser_agent - INFO - 🛑 Termination requested, aborting agent execution
2025-07-30 11:46:13,590 - browser_agent - INFO - 📊 Parsed 1 console messages from MCP data
2025-07-30 11:46:13,590 - browser_agent - INFO - 📊 Collected 1 console messages
2025-07-30 11:46:13,590 - browser_agent - INFO - 📊 Crawling Statistics:
2025-07-30 11:46:13,590 - browser_agent - INFO -    - Total interactions: 72
2025-07-30 11:46:13,590 - browser_agent - INFO -    - Elements discovered: 0
2025-07-30 11:46:13,590 - browser_agent - INFO -    - Elements interacted: 0
2025-07-30 11:46:13,590 - browser_agent - INFO -    - Network requests: 0
2025-07-30 11:46:13,590 - browser_agent - INFO -    - Console messages: 1
2025-07-30 11:46:13,590 - browser_agent - INFO - 🧹 Ensuring browser cleanup...
2025-07-30 11:46:18,418 - browser_agent - WARNING - ⏰ Browser cleanup timed out during termination
2025-07-30 11:46:18,418 - browser_agent - INFO - ✅ Termination handling complete
2025-07-30 11:46:18,457 - browser_agent - WARNING - ⏰ Browser cleanup timed out during termination
2025-07-30 11:46:18,457 - browser_agent - INFO - ✅ Termination handling complete
2025-07-30 11:46:18,477 - browser_agent - WARNING - ⏰ Browser cleanup timed out during termination
2025-07-30 11:46:18,478 - browser_agent - INFO - ✅ Termination handling complete
2025-07-30 11:46:18,496 - browser_agent - WARNING - ⏰ Browser cleanup timed out during termination
2025-07-30 11:46:18,496 - browser_agent - INFO - ✅ Termination handling complete
2025-07-30 11:46:18,515 - browser_agent - WARNING - ⏰ Browser cleanup timed out during termination
2025-07-30 11:46:18,515 - browser_agent - INFO - ✅ Termination handling complete
2025-07-30 11:46:18,534 - browser_agent - WARNING - ⏰ Browser cleanup timed out during termination
2025-07-30 11:46:18,534 - browser_agent - INFO - ✅ Termination handling complete
2025-07-30 11:46:18,554 - browser_agent - WARNING - ⏰ Browser cleanup timed out during termination
2025-07-30 11:46:18,557 - browser_agent - INFO - ✅ Termination handling complete
2025-07-30 11:46:18,570 - browser_agent - WARNING - ⏰ Browser cleanup timed out during termination
2025-07-30 11:46:18,571 - browser_agent - INFO - ✅ Termination handling complete
2025-07-30 11:46:23,552 - browser_agent - INFO - ✅ Browser cleanup command issued successfully.
2025-07-30 11:46:23,553 - browser_agent - INFO - ✅ Comprehensive crawling completed successfully
2025-07-30 11:46:25,555 - browser_agent - INFO - 💰 LLM Usage Summary:
2025-07-30 11:46:25,556 - browser_agent - INFO -    - Interactions: 7
2025-07-30 11:46:25,556 - browser_agent - INFO -    - Cost: $0.030790
2025-07-30 11:46:25,556 - browser_agent - INFO -    - Input tokens: 628
2025-07-30 11:46:25,556 - browser_agent - INFO -    - Output tokens: 2922
2025-07-30 11:46:25,558 - browser_agent - INFO - 🎯 Application Analysis: api/service (confidence: 0.50)
2025-07-30 11:46:25,563 - browser_agent - INFO - ✅ Comprehensive crawling complete. Report saved to: reports/recon_report_juice-shop.herokuapp.com_20250730_114625.json
