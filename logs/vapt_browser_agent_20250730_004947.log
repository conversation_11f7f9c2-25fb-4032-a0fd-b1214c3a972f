2025-07-30 00:49:47,046 - browser_agent - INFO - <PERSON><PERSON> logging initialized successfully
2025-07-30 00:49:47,046 - browser_agent - INFO - Browser Agent initialized for intelligent single-page deep analysis.
2025-07-30 00:49:47,047 - browser_agent - INFO - Starting deep analysis for single page: https://juice-shop.herokuapp.com
2025-07-30 00:49:47,047 - browser_agent - INFO - Connecting to MCP server...
2025-07-30 00:49:47,107 - browser_agent - INFO - Connected to MCP server successfully
2025-07-30 00:49:47,107 - browser_agent - INFO - Issuing deep analysis task order for: https://juice-shop.herokuapp.com
2025-07-30 00:50:08,992 - browser_agent - WARNING - 🛑 Received termination signal 2
2025-07-30 00:50:11,776 - browser_agent - INFO - 🛑 Handling termination: signal_interrupt
2025-07-30 00:50:11,776 - browser_agent - INFO - 🔄 Attempting to close browser before termination...
2025-07-30 00:50:11,777 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-30 00:50:16,778 - browser_agent - WARNING - ⏰ Browser cleanup timed out during termination
2025-07-30 00:50:16,779 - browser_agent - INFO - ✅ Termination handling complete
2025-07-30 00:50:25,299 - browser_agent - INFO - 🛑 Termination requested during analysis
2025-07-30 00:50:26,302 - browser_agent - INFO - LLM Usage Summary: 1 interactions, $0.006190 cost, 92 input tokens, 596 output tokens
2025-07-30 00:50:26,305 - browser_agent - INFO - Analysis complete. Report saved to: reports/recon_report_juice-shop.herokuapp.com_20250730_005026.json
