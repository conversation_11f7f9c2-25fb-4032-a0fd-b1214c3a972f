2025-07-30 01:16:22,212 - browser_agent - INFO - <PERSON><PERSON> logging initialized successfully
2025-07-30 01:16:22,212 - browser_agent - INFO - Browser Agent initialized for intelligent single-page deep analysis.
2025-07-30 01:16:22,213 - browser_agent - INFO - Starting deep analysis for single page: https://httpbin.org/html
2025-07-30 01:16:22,213 - browser_agent - INFO - Connecting to MCP server...
2025-07-30 01:16:22,316 - browser_agent - INFO - Connected to MCP server successfully
2025-07-30 01:16:22,316 - browser_agent - INFO - 🔍 Available MCP methods: ['initialize', 'register']
2025-07-30 01:16:22,317 - browser_agent - INFO - Issuing deep analysis task order for: https://httpbin.org/html
2025-07-30 01:16:22,317 - browser_agent - INFO - 🔄 Real-time data collection enabled - network and console data will be captured after each interaction
2025-07-30 01:16:32,041 - browser_agent - INFO - Attempting to collect final network and console data from <PERSON><PERSON> MCP...
2025-07-30 01:16:34,643 - browser_agent - ERROR - Failed to collect Playwright data: cannot access local variable 're' where it is not associated with a value
2025-07-30 01:16:34,643 - browser_agent - INFO - 📊 Final data collection summary: 0 network logs, 0 console logs, 0 raw requests collected
2025-07-30 01:16:34,643 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-30 01:16:37,962 - browser_agent - WARNING - 🛑 Received termination signal 1
2025-07-30 01:16:38,594 - browser_agent - INFO - 🛑 Handling termination: signal_interrupt
2025-07-30 01:16:38,595 - browser_agent - INFO - 🔄 Attempting to close browser before termination...
2025-07-30 01:16:38,595 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-30 01:16:42,063 - browser_agent - INFO - Browser cleanup command issued successfully.
2025-07-30 01:16:42,065 - browser_agent - INFO - 🚨 Emergency report saved to: reports/recon_report_httpbin.org_20250730_011642_partial.json
2025-07-30 01:16:42,065 - browser_agent - INFO - ✅ Termination handling complete
2025-07-30 01:16:42,088 - browser_agent - INFO - Browser cleanup command issued successfully.
2025-07-30 01:16:42,089 - browser_agent - INFO - Exhaustive analysis completed. App type: unknown
2025-07-30 01:16:43,092 - browser_agent - INFO - LLM Usage Summary: 3 interactions, $0.004378 cost, 539 input tokens, 303 output tokens
2025-07-30 01:16:43,093 - browser_agent - INFO - Analysis complete. Report saved to: reports/recon_report_httpbin.org_20250730_011643.json
