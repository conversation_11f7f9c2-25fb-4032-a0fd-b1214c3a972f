2025-07-30 15:15:18,275 - browser_agent - INFO - <PERSON><PERSON> logging initialized successfully
2025-07-30 15:15:18,275 - browser_agent - INFO - Comprehensive Browser Agent initialized for exhaustive web crawling.
2025-07-30 15:15:18,276 - browser_agent - INFO - 🚀 Starting comprehensive web crawling for: https://brokencrystals.com/
2025-07-30 15:15:18,276 - browser_agent - INFO - Connecting to MCP server...
2025-07-30 15:15:18,381 - browser_agent - INFO - Connected to MCP server successfully
2025-07-30 15:15:18,381 - browser_agent - INFO - 🔍 Starting comprehensive single-page analysis of: https://brokencrystals.com/
2025-07-30 15:15:18,381 - browser_agent - INFO - 🔄 Executing single-page analysis phase: initial_navigation
2025-07-30 15:15:40,765 - browser_agent - INFO - ✅ Single-page phase initial_navigation completed successfully
2025-07-30 15:15:40,765 - browser_agent - INFO - 🔄 Executing single-page analysis phase: element_interaction
2025-07-30 15:16:24,470 - browser_agent - INFO - ✅ Single-page phase element_interaction completed successfully
2025-07-30 15:16:24,470 - browser_agent - INFO - 🔄 Executing single-page analysis phase: form_testing
2025-07-30 15:16:52,154 - browser_agent - INFO - ✅ Single-page phase form_testing completed successfully
2025-07-30 15:16:52,154 - browser_agent - INFO - 🔄 Executing single-page analysis phase: scroll_hover
2025-07-30 15:17:14,570 - browser_agent - INFO - ✅ Single-page phase scroll_hover completed successfully
2025-07-30 15:17:14,570 - browser_agent - INFO - 🔄 Executing single-page analysis phase: final_collection
2025-07-30 15:17:54,563 - browser_agent - INFO - ✅ Single-page phase final_collection completed successfully
2025-07-30 15:17:54,563 - browser_agent - INFO - 🌐 Collecting comprehensive network data...
2025-07-30 15:18:35,957 - browser_agent - ERROR - Agent execution failed on attempt 1: This model's maximum context length is 128000 tokens. However, your messages resulted in 256916 tokens (255735 in the messages, 1181 in the functions). Please reduce the length of the messages or functions.
2025-07-30 15:19:41,445 - browser_agent - ERROR - Agent execution failed on attempt 2: This model's maximum context length is 128000 tokens. However, your messages resulted in 194466 tokens (193285 in the messages, 1181 in the functions). Please reduce the length of the messages or functions.
2025-07-30 15:20:47,239 - browser_agent - ERROR - Agent execution failed on attempt 3: This model's maximum context length is 128000 tokens. However, your messages resulted in 194466 tokens (193285 in the messages, 1181 in the functions). Please reduce the length of the messages or functions.
2025-07-30 15:20:47,239 - browser_agent - ERROR - ❌ Failed to collect network data: This model's maximum context length is 128000 tokens. However, your messages resulted in 194466 tokens (193285 in the messages, 1181 in the functions). Please reduce the length of the messages or functions.
2025-07-30 15:20:47,239 - browser_agent - INFO - 📝 Collecting comprehensive console data...
2025-07-30 15:20:50,477 - browser_agent - INFO - 📊 Enhanced console parsing found 5 console messages
2025-07-30 15:20:50,477 - browser_agent - INFO - 📊 Collected 5 console messages
2025-07-30 15:20:50,477 - browser_agent - INFO - 📊 Crawling Statistics:
2025-07-30 15:20:50,477 - browser_agent - INFO -    - Total interactions: 125
2025-07-30 15:20:50,477 - browser_agent - INFO -    - Elements discovered: 0
2025-07-30 15:20:50,477 - browser_agent - INFO -    - Elements interacted: 0
2025-07-30 15:20:50,477 - browser_agent - INFO -    - Network requests: 0
2025-07-30 15:20:50,477 - browser_agent - INFO -    - Console messages: 5
2025-07-30 15:20:50,477 - browser_agent - INFO - 🧹 Ensuring browser cleanup...
2025-07-30 15:20:53,001 - browser_agent - INFO - ✅ Browser cleanup command issued successfully.
2025-07-30 15:20:53,001 - browser_agent - INFO - ✅ Comprehensive single-page analysis completed successfully
2025-07-30 15:20:55,003 - browser_agent - INFO - 💰 LLM Usage Summary:
2025-07-30 15:20:55,004 - browser_agent - INFO -    - Interactions: 9
2025-07-30 15:20:55,004 - browser_agent - INFO -    - Cost: $0.034368
2025-07-30 15:20:55,005 - browser_agent - INFO -    - Input tokens: 1531
2025-07-30 15:20:55,005 - browser_agent - INFO -    - Output tokens: 3054
2025-07-30 15:20:55,007 - browser_agent - INFO - 🎯 Application Analysis: social/community (confidence: 0.67)
2025-07-30 15:20:55,011 - browser_agent - INFO - ✅ Comprehensive crawling complete. Report saved to: reports/recon_report_brokencrystals.com_20250730_152055.json
