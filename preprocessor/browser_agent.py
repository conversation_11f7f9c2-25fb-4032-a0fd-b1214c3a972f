"""
Browser Agent - Comprehensive Web Crawler and Reconnaissance Tool

This agent performs exhaustive web crawling using <PERSON><PERSON> Playwright with:
- Complete page interaction (click, hover, scroll every element)
- Real-time network and console logging after each interaction
- Raw HTTP request capture with complete headers and bodies
- LLM prompt, token, and cost tracking
- Robust error handling and session management
"""

import json
import asyncio
import time
import signal
import atexit
from datetime import datetime
from typing import Dict, Any, List, Optional, Set, Tuple
from pathlib import Path
from contextlib import asynccontextmanager
from urllib.parse import urlparse, parse_qs
import re
import hashlib

from agno.agent import Agent

# Note: agno.tools.mcp might not be available in current agno version
try:
    from agno.tools.mcp import MCPTools
except ImportError:
    # Use fallback from mcp_manager
    from shared.mcp_manager import MCPTools

import sys

# Add parent directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from shared.config import VAPTConfig, get_vapt_config
from shared.llm_factory import LLMFactory
from shared.logger import get_logger
from shared.mcp_manager import MCPManager

# Import LLM logging system
try:
    from LLM_logs import get_llm_logger
    LLM_LOGGING_AVAILABLE = True
except ImportError:
    LLM_LOGGING_AVAILABLE = False

class BrowserAgent:
    """
    Comprehensive Browser Agent for exhaustive web crawling and reconnaissance.

    Features:
    - Complete page interaction (click, hover, scroll every element)
    - Real-time network and console logging after each interaction
    - Raw HTTP request capture with complete headers and bodies
    - LLM prompt, token, and cost tracking
    - Robust error handling and session management
    """

    def __init__(self, config: Optional[VAPTConfig] = None):
        self.config = config or get_vapt_config()
        self.logger = get_logger("browser_agent", self.config.log_level)
        self.llm = LLMFactory.create_llm(self.config.llm)
        self.mcp_manager = MCPManager(self.config)

        # Core state tracking
        self.target_url = ""
        self.interaction_counter = 0
        self.browser_closed = False
        self.termination_requested = False
        self.current_agent = None

        # Interaction tracking
        self.interacted_elements = set()  # Track elements we've already interacted with
        self.discovered_elements = []     # All elements found during crawling
        self.current_snapshot_data = None # Current page snapshot for element discovery

        # Initialize LLM logging if available
        self.llm_logger = None
        if LLM_LOGGING_AVAILABLE:
            try:
                self.llm_logger = get_llm_logger()
                self.logger.info("LLM logging initialized successfully")
            except Exception as e:
                self.logger.warning(f"Failed to initialize LLM logging: {e}")

        # Set up termination handlers
        self._setup_termination_handlers()

        # Enhanced report data structure for comprehensive crawling
        self.report_data = {
            "metadata": {
                "target_url": None,
                "start_time": None,
                "end_time": None,
                "duration": None,
                "total_interactions": 0,
                "total_elements_discovered": 0,
                "total_elements_interacted": 0,
                "total_network_requests": 0,
                "total_console_messages": 0,
                "total_scroll_actions": 0,
                "total_form_submissions": 0,
                "browser_closed": False,
                "llm_interactions": 0,
                "llm_cost_usd": 0.0,
                "llm_input_tokens": 0,
                "llm_output_tokens": 0
            },
            "crawling_phases": [],           # Track different phases of crawling
            "element_discovery": [],         # All discovered elements
            "interaction_logs": [],          # Detailed interaction logs
            "network_logs": [],              # Network requests after each interaction
            "console_logs": [],              # Console messages after each interaction
            "raw_requests": [],              # Complete raw HTTP requests
            "scroll_interactions": [],       # Scroll actions performed
            "form_submissions": [],          # Form submission attempts
            "hover_interactions": [],        # Hover actions performed
            "click_interactions": [],        # Click actions performed
            "llm_interactions": [],          # LLM prompt/response logs
            "session_snapshots": [],         # Page snapshots at different stages
            "error_logs": [],                # Errors encountered during crawling
            "application_analysis": {
                "detected_type": "unknown",
                "confidence": 0.0,
                "features": [],
                "technology_stack": [],
                "reasoning": ""
            }
        }

        self.logger.info("Comprehensive Browser Agent initialized for exhaustive web crawling.")

    def _setup_termination_handlers(self):
        """Set up signal handlers for graceful termination"""
        def signal_handler(signum, frame):
            self.logger.warning(f"🛑 Received termination signal {signum}")
            self.termination_requested = True
            asyncio.create_task(self._handle_termination("signal_interrupt"))

        # Register signal handlers for common termination signals
        try:
            signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
            signal.signal(signal.SIGTERM, signal_handler)  # Termination request
            if hasattr(signal, 'SIGHUP'):  # Unix systems
                signal.signal(signal.SIGHUP, signal_handler)
        except (ValueError, OSError) as e:
            self.logger.warning(f"Could not set up all signal handlers: {e}")

        # Register cleanup function for normal exit
        atexit.register(self._cleanup_on_exit)

    def _cleanup_on_exit(self):
        """Cleanup function called on normal program exit"""
        if not self.browser_closed and not self.termination_requested:
            self.logger.info("🧹 Performing cleanup on exit")
            # Note: This runs in sync context, so we can't use async cleanup
            self.termination_requested = True

    async def _handle_immediate_termination(self, agent: Agent, reason: str):
        """Handle immediate termination request with rapid cleanup and report saving."""
        self.logger.warning(f"🛑 IMMEDIATE TERMINATION: {reason}")

        try:
            # Set termination flag
            self.termination_requested = True

            # Rapid browser cleanup with short timeout
            if not self.browser_closed:
                try:
                    self.logger.info("⚡ Rapid browser cleanup...")
                    cleanup_prompt = "URGENT: Call browser_close() immediately to terminate the browser session."
                    await asyncio.wait_for(agent.arun(cleanup_prompt), timeout=3.0)
                    self.browser_closed = True
                except asyncio.TimeoutError:
                    self.logger.warning("⏰ Rapid browser cleanup timed out")
                except Exception as e:
                    self.logger.warning(f"⚠️ Rapid cleanup error: {e}")

            # Immediate report saving
            await self._save_immediate_termination_report(reason)

            self.logger.info("✅ Immediate termination handling complete")

        except Exception as e:
            self.logger.error(f"❌ Error during immediate termination: {e}")

    async def _handle_termination(self, reason: str):
        """Handle termination request gracefully"""
        if self.termination_requested:
            self.logger.info(f"🛑 Handling graceful termination: {reason}")

            # Try to close browser if we have an active agent
            if self.current_agent and not self.browser_closed:
                try:
                    self.logger.info("🔄 Attempting to close browser before termination...")
                    await asyncio.wait_for(
                        self._ensure_browser_cleanup(self.current_agent),
                        timeout=5.0
                    )
                except asyncio.TimeoutError:
                    self.logger.warning("⏰ Browser cleanup timed out during termination")
                except Exception as e:
                    self.logger.error(f"❌ Error during termination cleanup: {e}")

            # Save emergency report if we have data
            if self.target_url and (self.report_data.get("crawling_phases") or self.report_data.get("interaction_logs")):
                await self._emergency_save_report(self.target_url, f"terminated_{reason}")

            self.logger.info("✅ Termination handling complete")
    
    @asynccontextmanager
    async def _get_mcp_tools(self):
        """Get MCP tools connection with comprehensive error handling"""
        self.logger.info("Connecting to MCP server...")
        try:
            async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
                self.logger.info("Connected to MCP server successfully")
                yield mcp_tools
        except Exception as e:
            self.logger.error(f"Failed to connect to MCP server: {e}")
            self.logger.info("Make sure MCP server is running at: http://localhost:8931")
            self.logger.info("Start it with: node cli.js --browser chrome --port 8931")
            raise
    
    async def run_reconnaissance(self, target_url: str) -> str:
        """
        Run comprehensive web crawling and reconnaissance on target URL.

        This method performs exhaustive crawling by:
        1. Navigating to the target URL
        2. Taking initial page snapshot
        3. Discovering all interactive elements
        4. Systematically interacting with each element (click, hover, scroll)
        5. Capturing network and console logs after each interaction
        6. Recording complete raw HTTP requests
        7. Tracking LLM usage and costs

        Args:
            target_url: Target URL to crawl

        Returns:
            Path to the generated comprehensive JSON report
        """
        start_time = datetime.now()
        self.target_url = target_url
        self.logger.info(f"🚀 Starting comprehensive web crawling for: {target_url}")

        # Initialize metadata
        self.report_data["metadata"].update({
            "target_url": target_url,
            "start_time": start_time.isoformat(),
            "end_time": None,
            "duration": None
        })

        try:
            async with self._get_mcp_tools() as mcp_tools:
                # Create specialized single-page analysis agent
                agent = Agent(
                    name="Single-Page Web Analyzer",
                    role="You are an expert single-page web analyzer that comprehensively analyzes ONE specific webpage. You interact with every element while maintaining focus on the target page, handling navigation by returning to the original page.",
                    goal=f"Comprehensively analyze the single webpage {target_url} by interacting with every element while staying focused on this specific page.",
                    tools=[mcp_tools],
                    model=self.llm,
                    debug_mode=True,
                    show_tool_calls=True,
                    instructions=self._get_single_page_crawling_instructions(),
                    memory=True
                )

                # Track current agent for termination handling
                self.current_agent = agent

                # Execute comprehensive crawling
                await self._execute_comprehensive_crawling(agent, target_url)
                await asyncio.sleep(2)  # Allow final network requests to complete

        except KeyboardInterrupt:
            self.logger.warning("🛑 Single-page analysis interrupted by user")
            self.termination_requested = True
            if self.current_agent:
                await self._handle_immediate_termination(self.current_agent, "keyboard_interrupt")
            else:
                await self._handle_termination("keyboard_interrupt")
            raise
        except Exception as e:
            self.logger.error(f"❌ Single-page analysis failed: {e}")
            if not self.termination_requested:
                await self._emergency_save_report(target_url, f"error: {str(e)[:100]}")
            await asyncio.sleep(1)
            raise
        finally:
            # Clear current agent reference
            self.current_agent = None

        # Finalize report with comprehensive metadata
        await self._finalize_report(start_time)

        # Save comprehensive report
        report_path = self._save_report(target_url)
        self.logger.info(f"✅ Comprehensive crawling complete. Report saved to: {report_path}")

        return report_path

    async def _save_immediate_termination_report(self, reason: str):
        """Save report immediately upon termination request."""
        try:
            self.logger.info("💾 Saving immediate termination report...")

            end_time = datetime.now()
            self.report_data["metadata"]["end_time"] = end_time.isoformat()
            self.report_data["metadata"]["status"] = "terminated"
            self.report_data["metadata"]["termination_reason"] = reason
            self.report_data["metadata"]["browser_closed"] = self.browser_closed

            # Add termination metadata
            self.report_data["metadata"]["total_interactions"] = self.interaction_counter
            self.report_data["metadata"]["total_elements_discovered"] = len(self.discovered_elements)
            self.report_data["metadata"]["total_elements_interacted"] = len(self.interacted_elements)

            # Add LLM summary if available
            if self.llm_logger:
                try:
                    summary = self.llm_logger.get_session_summary()
                    self.report_data["metadata"]["llm_interactions"] = summary.get("total_interactions", 0)
                    self.report_data["metadata"]["llm_cost_usd"] = summary.get("total_cost_usd", 0.0)
                except:
                    pass

            report_path = self._save_report(self.target_url, suffix="_terminated")
            self.logger.info(f"✅ Immediate termination report saved to: {report_path}")

        except Exception as e:
            self.logger.error(f"❌ Failed to save immediate termination report: {e}")

    async def _emergency_save_report(self, target_url: str, reason: str):
        """Save report on errors or interruption"""
        try:
            end_time = datetime.now()
            self.report_data["metadata"]["end_time"] = end_time.isoformat()
            self.report_data["metadata"]["status"] = "partial"
            self.report_data["metadata"]["termination_reason"] = reason
            report_path = self._save_report(target_url, suffix="_partial")
            self.logger.info(f"🚨 Emergency report saved to: {report_path}")
        except Exception as e:
            self.logger.error(f"Failed to save emergency report: {e}")

    def _save_report(self, target_url: str, suffix: str = "") -> str:
        """Save the final report to a JSON file."""
        try:
            domain = urlparse(target_url).netloc.replace(":", "_")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"recon_report_{domain}_{timestamp}{suffix}.json"
            reports_dir = Path("reports")
            reports_dir.mkdir(exist_ok=True)
            report_path = reports_dir / filename
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(self.report_data, f, indent=2, ensure_ascii=False)
            return str(report_path)
        except Exception as e:
            self.logger.error(f"Error saving report: {e}")
            try:
                backup_path = f"emergency_report_{int(time.time())}.json"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    json.dump(self.report_data, f, indent=2, ensure_ascii=False)
                return backup_path
            except:
                return "report_save_failed"

    def _get_single_page_crawling_instructions(self) -> str:
        """
        Instructions for comprehensive single-page analysis with link handling.
        """
        return """
You are an expert single-page web analyzer that performs COMPREHENSIVE analysis of ONE specific webpage. Your mission is to interact with EVERY element on the target page while staying focused on that single page.

🎯 **CORE MISSION**: Analyze the single target webpage comprehensively by interacting with every element while capturing all network/console data.

🚨 **CRITICAL SINGLE-PAGE RULES**:
1. **STAY ON TARGET PAGE**: You are analyzing ONE specific webpage only
2. **LINK HANDLING**: If clicking a link navigates away from the target page:
   - IMMEDIATELY use `browser_navigate_back` to return to the target page
   - Continue analysis on the original page
   - Log the navigation attempt for reporting
3. **TAB MANAGEMENT**: If a click opens a new tab:
   - Use `browser_tab_list` to see all tabs
   - Use `browser_tab_close` to close the new tab
   - Continue on the original tab with the target page
4. **FOCUS MAINTENANCE**: Always return to and continue analyzing the original target page

🔧 **AVAILABLE MCP PLAYWRIGHT TOOLS**:
🌐 NAVIGATION & PAGE MANAGEMENT:
- Navigate to URLs with `browser_navigate`
- Go back with `browser_navigate_back` (CRITICAL for link handling)
- Go forward with `browser_navigate_forward`

📸 VISUAL & CONTENT CAPTURE:
- Take page snapshots with `browser_snapshot`
- Take screenshots with `browser_take_screenshot`

🖱️ INTERACTION TOOLS:
- Click elements with `browser_click`
- Type text with `browser_type`
- Hover over elements with `browser_hover`
- Select dropdown options with `browser_select_option`
- Press keyboard keys with `browser_press_key`
- Wait for elements/text/time with `browser_wait_for`

� TAB MANAGEMENT (for new tab handling):
- List all tabs with `browser_tab_list`
- Switch between tabs with `browser_tab_select`
- Close tabs with `browser_tab_close`

🔍 MONITORING TOOLS:
- View console messages with `browser_console_messages`
- Monitor network requests with `browser_network_requests`
- Get detailed request info with `browser_network_request_details`

⚡ **SINGLE-PAGE ANALYSIS PROTOCOL**:

1. **INITIAL SETUP**:
   - Navigate to the target URL using `browser_navigate`
   - Take initial snapshot with `browser_snapshot` to discover all elements
   - Capture initial network/console state

2. **ELEMENT DISCOVERY & INTERACTION**:
   - From the snapshot, identify ALL interactive elements on the current page
   - For EACH element discovered:
     a) Interact with it (click, hover, type, etc.)
     b) Check if navigation occurred - if yes, use `browser_navigate_back`
     c) Check for new tabs - if yes, close them and return to original
     d) IMMEDIATELY capture network requests with `browser_network_requests`
     e) IMMEDIATELY capture console messages with `browser_console_messages`
     f) If the page content changes significantly, take a new `browser_snapshot`

3. **COMPREHENSIVE ON-PAGE INTERACTIONS**:
   - **BUTTONS**: Click every button (but return if it navigates away)
   - **LINKS**: Click every link (but immediately navigate back)
   - **FORMS**: Fill and submit every form with test data
   - **INPUTS**: Test every input field, dropdown, checkbox, radio button
   - **HOVER**: Hover over elements that might reveal tooltips or menus
   - **SCROLL**: Scroll through the entire page (top, middle, bottom)

4. **NAVIGATION HANDLING WORKFLOW**:
   ```
   For each clickable element:
   1. Click the element
   2. Check current URL - if different from target, use browser_navigate_back
   3. Check tabs - if new tab opened, close it
   4. Capture network/console data
   5. Continue with next element
   ```

5. **DATA CAPTURE REQUIREMENTS**:
   - Capture network/console data after EVERY interaction
   - Use `browser_network_request_details` for complete raw HTTP data
   - Document every navigation attempt and return action
   - Track all discovered elements and their behaviors

6. **TERMINATION HANDLING**:
   - Monitor for termination signals during execution
   - If termination requested, immediately save current data and close browser
   - Ensure graceful cleanup even during interruption

🚨 **CRITICAL SUCCESS FACTORS**:
- NEVER leave the target page permanently
- ALWAYS return to target page after any navigation
- ALWAYS close new tabs and return to original
- ALWAYS capture data after each interaction
- Handle termination requests immediately
- Provide detailed logging of every action and navigation

Start by taking a snapshot to understand the page structure, then systematically interact with every element while maintaining focus on the single target page.
"""

    async def _execute_comprehensive_crawling(self, agent: Agent, target_url: str):
        """Execute comprehensive single-page analysis with systematic element interaction."""
        try:
            # Check for termination before starting
            if self.termination_requested:
                self.logger.info("🛑 Termination requested, skipping single-page analysis")
                await self._handle_immediate_termination(agent, "pre_analysis_termination")
                return

            self.logger.info(f"🔍 Starting comprehensive single-page analysis of: {target_url}")

            # Phase 1: Initial Navigation and Discovery
            if self.termination_requested:
                await self._handle_immediate_termination(agent, "phase1_pre_termination")
                return

            await self._execute_crawling_phase(agent, "initial_navigation", f"""
🚀 PHASE 1: INITIAL NAVIGATION AND DISCOVERY

Your task: Navigate to {target_url} and perform initial discovery.

CRITICAL: This is your TARGET PAGE - remember this URL and always return to it.

Steps:
1. Navigate to the URL using `browser_navigate`
2. Take initial snapshot with `browser_snapshot` to discover all elements
3. Capture initial network requests with `browser_network_requests`
4. Capture initial console messages with `browser_console_messages`
5. Document all discovered interactive elements on THIS PAGE ONLY

Remember: {target_url} is your target page. Always return to this page if you navigate away.
""")

            # Phase 2: Systematic Element Interaction with Link Handling
            if self.termination_requested:
                await self._handle_immediate_termination(agent, "phase2_pre_termination")
                return

            await self._execute_crawling_phase(agent, "element_interaction", f"""
🎯 PHASE 2: SYSTEMATIC ELEMENT INTERACTION (SINGLE PAGE FOCUS)

Your task: Interact with EVERY discoverable element on {target_url} while staying on this page.

TARGET PAGE: {target_url} (ALWAYS RETURN TO THIS PAGE)

For each interactive element you find:
1. Interact with it (click, hover, type, etc.)
2. CHECK: Did this navigate away from {target_url}?
   - If YES: IMMEDIATELY use `browser_navigate_back` to return
   - If new tab opened: Use `browser_tab_close` to close it
3. IMMEDIATELY call `browser_network_requests` to capture network activity
4. IMMEDIATELY call `browser_console_messages` to capture console output
5. If page content changes, take new `browser_snapshot`
6. Use `browser_network_request_details` with request IDs for raw HTTP data

CRITICAL: Never leave {target_url} permanently. Always return to continue analysis.
""")

            # Phase 3: Form and Input Testing
            if self.termination_requested:
                await self._handle_immediate_termination(agent, "phase3_pre_termination")
                return

            await self._execute_crawling_phase(agent, "form_testing", f"""
📝 PHASE 3: COMPREHENSIVE FORM AND INPUT TESTING (SINGLE PAGE)

Your task: Test all forms and inputs on {target_url} while staying on this page.

TARGET PAGE: {target_url} (STAY FOCUSED ON THIS PAGE)

For each form/input:
1. Fill with test data (use realistic values like "<EMAIL>", "TestPassword123!")
2. Submit or trigger the input
3. CHECK: Did form submission navigate away? If yes, use `browser_navigate_back`
4. IMMEDIATELY capture network requests with `browser_network_requests`
5. IMMEDIATELY capture console messages with `browser_console_messages`
6. Get detailed request data with `browser_network_request_details`

Test every form, input field, dropdown, checkbox, and radio button on the target page.
""")

            # Phase 4: Scroll and Hover Interactions
            if self.termination_requested:
                await self._handle_immediate_termination(agent, "phase4_pre_termination")
                return

            await self._execute_crawling_phase(agent, "scroll_hover", f"""
🖱️ PHASE 4: SCROLL AND HOVER INTERACTIONS (SINGLE PAGE)

Your task: Perform scroll and hover interactions on {target_url}.

TARGET PAGE: {target_url} (MAINTAIN FOCUS ON THIS PAGE)

Actions:
1. Scroll through entire page (top, middle, bottom)
2. Hover over elements that might reveal tooltips or menus
3. After each action, capture network/console data
4. Look for lazy-loaded content or dynamic elements
5. If any hover action causes navigation, return with `browser_navigate_back`

Ensure comprehensive coverage while maintaining focus on the target page.
""")

            # Phase 5: Final Data Collection and Cleanup
            if self.termination_requested:
                await self._handle_immediate_termination(agent, "phase5_pre_termination")
                return

            await self._execute_crawling_phase(agent, "final_collection", f"""
🔍 PHASE 5: FINAL DATA COLLECTION AND CLEANUP (SINGLE PAGE)

Your task: Collect final comprehensive data from {target_url} and cleanup.

TARGET PAGE: {target_url} (ENSURE YOU'RE ON THIS PAGE FOR FINAL DATA)

Steps:
1. Verify you're on {target_url} - if not, use `browser_navigate` to return
2. Take final `browser_snapshot` to capture end state of the target page
3. Get complete network log with `browser_network_requests`
4. Get all console messages with `browser_console_messages`
5. For each network request ID, get detailed data with `browser_network_request_details`
6. Close browser with `browser_close`

CRITICAL: Ensure all data is captured from the target page before closing.
If termination is requested at any point, immediately proceed to browser cleanup.
""")

            # Final data collection with termination check
            if self.termination_requested:
                await self._handle_immediate_termination(agent, "final_data_collection_termination")
                return

            # Collect comprehensive network and console data
            await self._collect_comprehensive_network_data(agent)

            if self.termination_requested:
                await self._handle_immediate_termination(agent, "console_data_collection_termination")
                return

            await self._collect_comprehensive_console_data(agent)

            # Update final metadata
            await self._update_crawling_metadata()

            # Ensure browser cleanup
            await self._ensure_browser_cleanup(agent)

            self.logger.info("✅ Comprehensive single-page analysis completed successfully")

        except Exception as e:
            self.logger.error(f"❌ Single-page analysis execution failed: {e}")

            # Handle termination during error
            if self.termination_requested:
                await self._handle_immediate_termination(agent, f"error_termination: {str(e)[:50]}")
            else:
                try:
                    await self._ensure_browser_cleanup(agent)
                except:
                    pass
            raise

    async def _execute_crawling_phase(self, agent: Agent, phase_name: str, phase_prompt: str):
        """Execute a specific crawling phase with immediate termination handling."""
        try:
            # Check for termination before starting phase
            if self.termination_requested:
                self.logger.info(f"🛑 Termination requested, skipping phase: {phase_name}")
                await self._handle_immediate_termination(agent, f"phase_{phase_name}_skipped")
                return

            self.logger.info(f"🔄 Executing single-page analysis phase: {phase_name}")

            phase_start = datetime.now()

            # Enhanced prompt with termination awareness
            enhanced_prompt = f"""
{phase_prompt}

🚨 TERMINATION AWARENESS:
- Monitor for any interruption signals during execution
- If you detect termination is requested, immediately:
  1. Stop current interactions
  2. Capture current network/console data
  3. Call `browser_close` to cleanup
  4. Report current progress

TARGET PAGE REMINDER: {self.target_url} - Always return to this page if you navigate away.
"""

            phase_result = await self._execute_with_retry(agent, enhanced_prompt)
            phase_end = datetime.now()

            # Check for termination after phase execution
            if self.termination_requested:
                self.logger.info(f"🛑 Termination requested after phase {phase_name}")
                await self._handle_immediate_termination(agent, f"phase_{phase_name}_completed")
                return

            # Log phase completion
            phase_data = {
                "phase_name": phase_name,
                "start_time": phase_start.isoformat(),
                "end_time": phase_end.isoformat(),
                "duration": str(phase_end - phase_start),
                "result": phase_result[:2000] + "..." if len(phase_result) > 2000 else phase_result,
                "status": "completed",
                "target_url": self.target_url
            }

            self.report_data["crawling_phases"].append(phase_data)

            # Parse and categorize interactions from this phase
            await self._parse_phase_interactions(phase_result, phase_name)

            self.logger.info(f"✅ Single-page phase {phase_name} completed successfully")

        except Exception as e:
            self.logger.error(f"❌ Phase {phase_name} failed: {e}")

            # Check if failure was due to termination
            if self.termination_requested:
                await self._handle_immediate_termination(agent, f"phase_{phase_name}_terminated")
                return

            # Log failed phase
            phase_data = {
                "phase_name": phase_name,
                "start_time": datetime.now().isoformat(),
                "end_time": datetime.now().isoformat(),
                "duration": "0:00:00",
                "result": f"Phase failed: {str(e)}",
                "status": "failed",
                "error": str(e),
                "target_url": self.target_url
            }

            self.report_data["crawling_phases"].append(phase_data)
            self.report_data["error_logs"].append({
                "timestamp": datetime.now().isoformat(),
                "phase": phase_name,
                "error": str(e),
                "type": "phase_execution_error"
            })

            # Continue with next phase instead of failing completely
            self.logger.warning(f"⚠️ Continuing to next phase despite {phase_name} failure")

    async def _parse_phase_interactions(self, phase_result: str, phase_name: str):
        """Parse interactions from a crawling phase result."""
        lines = phase_result.lower().split('\n')

        # Enhanced patterns for comprehensive interaction detection
        interaction_patterns = {
            "click": ["clicking", "clicked", "click", "button", "link", "tab"],
            "hover": ["hovering", "hovered", "hover", "mouseover", "tooltip"],
            "scroll": ["scrolling", "scrolled", "scroll", "page down", "page up"],
            "type": ["typing", "typed", "type", "input", "text", "fill"],
            "form": ["form", "submit", "submitting", "submitted", "validation"],
            "network": ["network", "request", "response", "http", "api"],
            "console": ["console", "error", "warning", "log", "debug"],
            "snapshot": ["snapshot", "screenshot", "capture", "image"]
        }

        for line in lines:
            line_stripped = line.strip()
            if not line_stripped or len(line_stripped) < 10:
                continue

            timestamp = datetime.now().isoformat()

            # Categorize the interaction
            for interaction_type, patterns in interaction_patterns.items():
                if any(pattern in line_stripped for pattern in patterns):
                    interaction_data = {
                        "timestamp": timestamp,
                        "phase": phase_name,
                        "type": interaction_type,
                        "details": line_stripped,
                        "url": self.target_url
                    }

                    # Add to appropriate category
                    if interaction_type == "click":
                        self.report_data["click_interactions"].append(interaction_data)
                    elif interaction_type == "hover":
                        self.report_data["hover_interactions"].append(interaction_data)
                    elif interaction_type == "scroll":
                        self.report_data["scroll_interactions"].append(interaction_data)
                    elif interaction_type == "form":
                        self.report_data["form_submissions"].append(interaction_data)

                    # Also add to general interaction log
                    self.report_data["interaction_logs"].append(interaction_data)
                    self.interaction_counter += 1
                    break

    async def _update_crawling_metadata(self):
        """Update metadata with comprehensive crawling statistics."""
        try:
            self.report_data["metadata"].update({
                "total_interactions": self.interaction_counter,
                "total_elements_discovered": len(self.discovered_elements),
                "total_elements_interacted": len(self.interacted_elements),
                "total_network_requests": len(self.report_data.get("network_logs", [])),
                "total_console_messages": len(self.report_data.get("console_logs", [])),
                "total_scroll_actions": len(self.report_data.get("scroll_interactions", [])),
                "total_form_submissions": len(self.report_data.get("form_submissions", [])),
                "browser_closed": self.browser_closed
            })

            self.logger.info(f"📊 Crawling Statistics:")
            self.logger.info(f"   - Total interactions: {self.interaction_counter}")
            self.logger.info(f"   - Elements discovered: {len(self.discovered_elements)}")
            self.logger.info(f"   - Elements interacted: {len(self.interacted_elements)}")
            self.logger.info(f"   - Network requests: {len(self.report_data.get('network_logs', []))}")
            self.logger.info(f"   - Console messages: {len(self.report_data.get('console_logs', []))}")

        except Exception as e:
            self.logger.warning(f"Failed to update crawling metadata: {e}")

    async def _finalize_report(self, start_time: datetime):
        """Finalize the comprehensive crawling report."""
        try:
            end_time = datetime.now()
            self.report_data["metadata"]["end_time"] = end_time.isoformat()
            self.report_data["metadata"]["duration"] = str(end_time - start_time)

            # Add final LLM interaction summary
            if self.llm_logger:
                try:
                    summary = self.llm_logger.get_session_summary()
                    self.report_data["metadata"]["llm_interactions"] = summary.get("total_interactions", 0)
                    self.report_data["metadata"]["llm_cost_usd"] = summary.get("total_cost_usd", 0.0)
                    self.report_data["metadata"]["llm_input_tokens"] = summary.get("total_input_tokens", 0)
                    self.report_data["metadata"]["llm_output_tokens"] = summary.get("total_output_tokens", 0)

                    self.logger.info(f"💰 LLM Usage Summary:")
                    self.logger.info(f"   - Interactions: {summary.get('total_interactions', 0)}")
                    self.logger.info(f"   - Cost: ${summary.get('total_cost_usd', 0.0):.6f}")
                    self.logger.info(f"   - Input tokens: {summary.get('total_input_tokens', 0)}")
                    self.logger.info(f"   - Output tokens: {summary.get('total_output_tokens', 0)}")
                except Exception as e:
                    self.logger.warning(f"Failed to get LLM session summary: {e}")

            # Analyze application type based on collected data
            combined_content = "\n".join([
                phase.get("result", "") for phase in self.report_data.get("crawling_phases", [])
            ])
            app_analysis = self._analyze_application_type(combined_content, self.target_url)
            self.report_data["application_analysis"] = app_analysis

            self.logger.info(f"🎯 Application Analysis: {app_analysis['detected_type']} (confidence: {app_analysis['confidence']:.2f})")

        except Exception as e:
            self.logger.error(f"Failed to finalize report: {e}")

    async def _ensure_browser_cleanup(self, agent: Agent):
        """Ensure the browser is properly closed."""
        try:
            self.logger.info("🧹 Ensuring browser cleanup...")
            cleanup_prompt = "MANDATORY: Call browser_close() to terminate the browser session now."
            await asyncio.wait_for(agent.arun(cleanup_prompt), timeout=15)
            self.browser_closed = True
            self.report_data["metadata"]["browser_closed"] = True
            self.logger.info("✅ Browser cleanup command issued successfully.")
        except Exception as e:
            self.logger.warning(f"⚠️ Browser cleanup may have failed or timed out: {e}")
            self.browser_closed = False
            self.report_data["metadata"]["browser_closed"] = False
            
    async def _execute_with_retry(self, agent: Agent, prompt: str, max_retries: int = None) -> str:
        """Execute agent prompt with retry logic and termination handling."""
        if max_retries is None:
            max_retries = self.config.max_retries

        for attempt in range(max_retries):
            # Check for termination before each attempt
            if self.termination_requested:
                self.logger.info("🛑 Termination requested, aborting agent execution")
                return "Execution terminated by user request"

            try:
                # Log LLM interaction if logging is available
                if self.llm_logger:
                    with self.llm_logger.log_interaction(
                        model=str(agent.model),
                        provider="browser_agent",
                        prompt=prompt,
                        metadata={
                            "agent_name": agent.name,
                            "attempt": attempt + 1,
                            "target_url": self.target_url,
                            "interaction_type": "browser_analysis"
                        }
                    ) as ctx:
                        result = await asyncio.wait_for(
                            agent.arun(prompt),
                            timeout=300  # 5 minutes for exhaustive interaction
                        )
                        response_content = result.content if hasattr(result, 'content') else str(result)
                        self.llm_logger.complete_interaction(ctx, response_content)

                        # Store LLM interaction in report
                        self._log_llm_interaction(ctx, response_content, prompt)

                        return response_content
                else:
                    # Fallback without LLM logging
                    result = await asyncio.wait_for(
                        agent.arun(prompt),
                        timeout=300  # 5 minutes for exhaustive interaction
                    )
                    return result.content if hasattr(result, 'content') else str(result)

            except asyncio.TimeoutError:
                self.logger.warning(f"⏰ Agent attempt {attempt + 1} timed out after 5 minutes")
                if self.termination_requested:
                    return "Execution terminated during timeout"
                if attempt < max_retries - 1:
                    await asyncio.sleep(5)
                    continue
                else:
                    return f"Operation timed out after {max_retries} attempts."
            except Exception as e:
                self.logger.error(f"Agent execution failed on attempt {attempt + 1}: {e}")
                if self.termination_requested:
                    return f"Execution terminated during error: {e}"
                if attempt < max_retries - 1:
                    await asyncio.sleep(5)
                else:
                    raise e
        return "Failed to get a result from the agent after multiple retries."

    def _log_llm_interaction(self, interaction_context: Dict[str, Any], response: str, prompt: str):
        """Log LLM interaction to report data"""
        try:
            llm_interaction = {
                "interaction_id": interaction_context.get('interaction_id'),
                "timestamp": datetime.now().isoformat(),
                "model": interaction_context.get('model'),
                "provider": interaction_context.get('provider'),
                "prompt": prompt[:1000] + "..." if len(prompt) > 1000 else prompt,  # Truncate long prompts
                "response": response[:2000] + "..." if len(response) > 2000 else response,  # Truncate long responses
                "metadata": interaction_context.get('metadata', {}),
                "target_url": self.target_url
            }
            self.report_data["llm_interactions"].append(llm_interaction)

            # Update metadata counters
            self.report_data["metadata"]["llm_interactions"] = len(self.report_data["llm_interactions"])

            # Update cost if available
            if self.llm_logger:
                summary = self.llm_logger.get_session_summary()
                self.report_data["metadata"]["llm_cost_usd"] = summary.get("total_cost_usd", 0.0)

        except Exception as e:
            self.logger.warning(f"Failed to log LLM interaction: {e}")

    async def _collect_comprehensive_network_data(self, agent: Agent):
        """Collect comprehensive network data using MCP Playwright tools."""
        try:
            self.logger.info("🌐 Collecting comprehensive network data...")

            # Get all network requests with detailed prompting
            network_prompt = """CRITICAL: I need you to capture ALL network data from this browser session.

Step 1: Use browser_network_requests to get ALL network requests from this session.
Step 2: For EACH request ID returned, use browser_network_request_details to get the complete raw HTTP data.

I need COMPLETE details including:
- Full HTTP request headers and response headers
- Request and response bodies
- Status codes, timing information, and content types
- Raw HTTP request/response data

IMPORTANT: Show me the EXACT output from both browser_network_requests and browser_network_request_details tools. Do not summarize - give me the complete raw output."""

            network_result = await self._execute_with_retry(agent, network_prompt)

            # Enhanced parsing with better error handling
            network_logs, raw_requests = self._parse_network_data_enhanced(network_result)

            self.report_data["network_logs"] = network_logs
            self.report_data["raw_requests"] = raw_requests

            # Store raw output for debugging
            self.report_data["raw_network_output"] = network_result

            self.logger.info(f"📊 Collected {len(network_logs)} network requests and {len(raw_requests)} raw requests")

        except Exception as e:
            self.logger.error(f"❌ Failed to collect network data: {e}")
            self.report_data["error_logs"].append({
                "timestamp": datetime.now().isoformat(),
                "type": "network_collection_error",
                "error": str(e)
            })

    async def _collect_comprehensive_console_data(self, agent: Agent):
        """Collect comprehensive console data using MCP Playwright tools."""
        try:
            self.logger.info("📝 Collecting comprehensive console data...")

            console_prompt = """CRITICAL: I need you to capture ALL console messages from this browser session.

Use browser_console_messages to get ALL console messages from this session.
I need ALL console output including:
- JavaScript errors with full stack traces
- Network errors and warnings
- Debug messages and application logs
- Security-related console messages
- Any other browser console output

IMPORTANT: Show me the EXACT output from browser_console_messages tool. Do not summarize - give me the complete raw output with all console messages."""

            console_result = await self._execute_with_retry(agent, console_prompt)

            # Enhanced parsing with better extraction
            console_logs = self._parse_console_data_enhanced(console_result)

            self.report_data["console_logs"] = console_logs

            # Store raw output for debugging
            self.report_data["raw_console_output"] = console_result

            self.logger.info(f"📊 Collected {len(console_logs)} console messages")

        except Exception as e:
            self.logger.error(f"❌ Failed to collect console data: {e}")
            self.report_data["error_logs"].append({
                "timestamp": datetime.now().isoformat(),
                "type": "console_collection_error",
                "error": str(e)
            })

    def _extract_additional_network_details(self, network_output: str):
        """Extract additional network details from raw MCP output"""
        try:
            # Look for patterns that indicate more detailed network information
            lines = network_output.split('\n')

            # Track additional network statistics
            network_stats = {
                "total_requests": 0,
                "failed_requests": 0,
                "redirects": 0,
                "cached_requests": 0,
                "request_methods": {},
                "status_codes": {},
                "content_types": {},
                "domains": set()
            }

            for line in lines:
                line = line.strip()

                # Count requests by method
                for method in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS']:
                    if f'[{method}]' in line:
                        network_stats["total_requests"] += 1
                        network_stats["request_methods"][method] = network_stats["request_methods"].get(method, 0) + 1
                        break

                # Count status codes
                if '=>' in line and '[' in line and ']' in line:
                    try:
                        status_part = line.split('=>')[1].strip()
                        if '[' in status_part and ']' in status_part:
                            status_info = status_part.split('[')[1].split(']')[0]
                            status_code = status_info.split(' ')[0]
                            network_stats["status_codes"][status_code] = network_stats["status_codes"].get(status_code, 0) + 1

                            # Count failed requests (4xx, 5xx)
                            if status_code.startswith(('4', '5')):
                                network_stats["failed_requests"] += 1
                            # Count redirects (3xx)
                            elif status_code.startswith('3'):
                                network_stats["redirects"] += 1
                    except:
                        pass

                # Extract domains
                if 'http' in line:
                    import re
                    urls = re.findall(r'https?://([^/\s]+)', line)
                    for domain in urls:
                        network_stats["domains"].add(domain)

                # Look for content types
                if 'Content-Type:' in line:
                    try:
                        content_type = line.split('Content-Type:')[1].strip().split(';')[0]
                        network_stats["content_types"][content_type] = network_stats["content_types"].get(content_type, 0) + 1
                    except:
                        pass

            # Convert set to list for JSON serialization
            network_stats["domains"] = list(network_stats["domains"])

            # Add to report
            self.report_data["network_statistics"] = network_stats

            self.logger.info(f"Network Statistics: {network_stats['total_requests']} total requests, "
                           f"{network_stats['failed_requests']} failed, "
                           f"{len(network_stats['domains'])} unique domains")

        except Exception as e:
            self.logger.warning(f"Failed to extract additional network details: {e}")

    def _parse_network_data_enhanced(self, network_data: str) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Enhanced parsing of network data from MCP Playwright output."""
        network_logs = []
        raw_requests = []

        try:
            # First try JSON format parsing
            json_pattern = r'\{.*?"raw_request".*?"raw_response".*?\}'
            json_matches = re.findall(json_pattern, network_data, re.DOTALL)

            for json_match in json_matches:
                try:
                    # Try parsing as-is first
                    try:
                        raw_data = json.loads(json_match)
                        if isinstance(raw_data, dict) and "raw_request" in raw_data:
                            self._process_raw_network_data(raw_data, network_logs, raw_requests)
                            continue
                    except json.JSONDecodeError:
                        pass

                    # If that fails, try cleaning up the JSON
                    cleaned_json = json_match.replace('\\"', '"').replace('\\\\r\\\\n', '\\r\\n').replace('\\\\n', '\\n')
                    raw_data = json.loads(cleaned_json)

                    if isinstance(raw_data, dict) and "raw_request" in raw_data:
                        self._process_raw_network_data(raw_data, network_logs, raw_requests)

                except json.JSONDecodeError as e:
                    self.logger.debug(f"Failed to parse JSON match: {e}")
                    continue

            # Now try text format parsing (### Network Request ID: X format)
            text_blocks = self._parse_text_format_network_data(network_data)
            for text_data in text_blocks:
                self._process_raw_network_data(text_data, network_logs, raw_requests)

            # Also look for simple network request patterns in lines
            lines = network_data.split('\n')
            for line in lines:
                line = line.strip()

                if '[ID:' in line and '=>' in line:
                    try:
                        # Extract basic info from simple format
                        id_match = re.search(r'\[ID:\s*(\d+)\]', line)
                        req_id = id_match.group(1) if id_match else f"simple_{len(network_logs)}"

                        method_match = re.search(r'\]\s*\[([A-Z]+)\]', line)
                        method = method_match.group(1) if method_match else "GET"

                        url_match = re.search(r'\]\s*([^\s]+)\s*=>', line)
                        url = url_match.group(1) if url_match else "unknown"

                        status_match = re.search(r'=>\s*\[(\d+)\]', line)
                        status_code = status_match.group(1) if status_match else "unknown"

                        if url != "unknown":
                            network_log = {
                                "id": req_id,
                                "method": method,
                                "url": url,
                                "status_code": status_code,
                                "timestamp": datetime.now().isoformat(),
                                "source": "mcp_playwright_simple",
                                "has_raw_data": False,
                                "raw_line": line
                            }
                            network_logs.append(network_log)

                    except Exception as e:
                        self.logger.debug(f"Failed to parse simple network line: {line}, error: {e}")
                        continue

            self.logger.info(f"📊 Enhanced parsing found {len(network_logs)} network logs and {len(raw_requests)} raw requests")
            return network_logs, raw_requests

        except Exception as e:
            self.logger.error(f"❌ Enhanced network parsing failed: {e}")
            return [], []

    def _process_raw_network_data(self, raw_data: Dict[str, Any], network_logs: List[Dict[str, Any]], raw_requests: List[Dict[str, Any]]):
        """Process raw network data from MCP Playwright."""
        try:
            if "raw_request" in raw_data:
                request_lines = raw_data["raw_request"].split('\r\n')
                if request_lines:
                    first_line = request_lines[0]
                    parts = first_line.split(' ')
                    if len(parts) >= 3:
                        method = parts[0]
                        url_path = parts[1]

                        # Extract host from headers
                        host = ""
                        for req_line in request_lines[1:]:
                            if req_line.lower().startswith('host:'):
                                host = req_line.split(':', 1)[1].strip()
                                break

                        full_url = f"https://{host}{url_path}" if host else url_path

                        # Extract status from response
                        status_code = "unknown"
                        if "raw_response" in raw_data:
                            response_lines = raw_data["raw_response"].split('\r\n')
                            if response_lines:
                                status_line = response_lines[0]
                                if "HTTP/" in status_line:
                                    status_parts = status_line.split(' ')
                                    if len(status_parts) >= 2:
                                        status_code = status_parts[1]

                        # Create network log entry
                        network_log = {
                            "id": f"req_{len(network_logs)}",
                            "method": method,
                            "url": full_url,
                            "status_code": status_code,
                            "timestamp": datetime.now().isoformat(),
                            "source": "mcp_playwright_enhanced",
                            "has_raw_data": True
                        }
                        network_logs.append(network_log)

                        # Create raw request entry
                        raw_request = {
                            "id": f"req_{len(raw_requests)}",
                            "method": method,
                            "url": full_url,
                            "status_code": status_code,
                            "raw_request": raw_data.get("raw_request", ""),
                            "raw_response": raw_data.get("raw_response", ""),
                            "timestamp": datetime.now().isoformat(),
                            "complete": True,
                            "source": "mcp_playwright_enhanced"
                        }
                        raw_requests.append(raw_request)

        except Exception as e:
            self.logger.debug(f"Failed to process raw network data: {e}")

    def _extract_debug_network_data(self, network_data: str) -> List[Dict[str, Any]]:
        """Extract network data from MCP Playwright debug output format."""
        debug_blocks = []

        try:
            # Look for the specific debug format from terminal output
            lines = network_data.split('\n')
            i = 0

            while i < len(lines):
                line = lines[i].strip()

                # Look for debug tool call output with JSON
                if 'Tool call Id:' in line and i + 1 < len(lines):
                    next_line = lines[i + 1].strip()

                    # Check if next line contains JSON with raw_request
                    if next_line.startswith('{') and '"raw_request"' in next_line:
                        # Try to parse the JSON block
                        json_block = ""
                        j = i + 1
                        brace_count = 0

                        while j < len(lines):
                            current_line = lines[j].strip()
                            json_block += current_line

                            # Count braces to find end of JSON
                            brace_count += current_line.count('{') - current_line.count('}')

                            if brace_count == 0 and current_line.endswith('}'):
                                break
                            j += 1

                        # Try to parse the complete JSON block
                        try:
                            # Clean up the JSON - handle escaped quotes
                            cleaned_json = json_block.replace('\\"', '"').replace('\\\\', '\\')
                            raw_data = json.loads(cleaned_json)

                            if isinstance(raw_data, dict) and "raw_request" in raw_data:
                                debug_blocks.append(raw_data)

                        except json.JSONDecodeError as e:
                            self.logger.debug(f"Failed to parse debug JSON block: {e}")

                        i = j + 1
                    else:
                        i += 1
                else:
                    i += 1

            self.logger.debug(f"Extracted {len(debug_blocks)} debug network blocks")
            return debug_blocks

        except Exception as e:
            self.logger.debug(f"Failed to extract debug network data: {e}")
            return []

    def _parse_text_format_network_data(self, network_data: str) -> List[Dict[str, Any]]:
        """Parse network data from text format (### Network Request ID: X format)."""
        text_blocks = []

        try:
            # Split by network request blocks
            request_blocks = re.split(r'### Network Request ID: \d+', network_data)

            for block in request_blocks[1:]:  # Skip first empty block
                if not block.strip():
                    continue

                # Extract request and response sections
                request_section = ""
                response_section = ""

                lines = block.split('\n')
                in_request = False
                in_response = False

                for line in lines:
                    line = line.strip()

                    if line == "Request:" or line.startswith("Request:"):
                        in_request = True
                        in_response = False
                        continue
                    elif line == "Response:" or line.startswith("Response:"):
                        in_request = False
                        in_response = True
                        continue
                    elif line.startswith("```") or line.startswith("---"):
                        in_request = False
                        in_response = False
                        continue

                    if in_request and line:
                        request_section += line + "\r\n"
                    elif in_response and line:
                        response_section += line + "\r\n"

                # Create raw data structure if we have both request and response
                if request_section and response_section:
                    raw_data = {
                        "raw_request": request_section.strip(),
                        "raw_response": response_section.strip()
                    }
                    text_blocks.append(raw_data)

            self.logger.debug(f"Parsed {len(text_blocks)} text format network blocks")
            return text_blocks

        except Exception as e:
            self.logger.debug(f"Failed to parse text format network data: {e}")
            return []

    def _parse_mcp_network_logs(self, network_data: str) -> List[Dict[str, Any]]:
        """Parse network logs from MCP Playwright browser_network_requests output."""
        network_logs = []
        lines = network_data.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Look for network request patterns from MCP output
            # Format: [ID: X] [METHOD] URL => [STATUS] STATUS_TEXT
            if '[ID:' in line and '=>' in line:
                try:
                    # Extract request ID
                    id_match = re.search(r'\[ID:\s*(\d+)\]', line)
                    req_id = id_match.group(1) if id_match else "unknown"

                    # Extract method
                    method_match = re.search(r'\]\s*\[([A-Z]+)\]', line)
                    method = method_match.group(1) if method_match else "unknown"

                    # Extract URL and status
                    url_status_match = re.search(r'\]\s*([^\s]+)\s*=>\s*\[(\d+)\]\s*(.*)', line)
                    if url_status_match:
                        url = url_status_match.group(1)
                        status_code = url_status_match.group(2)
                        status_text = url_status_match.group(3).strip()
                    else:
                        url = "unknown"
                        status_code = "unknown"
                        status_text = ""

                    # Parse URL components
                    try:
                        parsed_url = urlparse(url)
                        domain = parsed_url.netloc
                        path = parsed_url.path
                        query_params = dict(parse_qs(parsed_url.query)) if parsed_url.query else {}
                    except:
                        domain = "unknown"
                        path = "unknown"
                        query_params = {}

                    network_log = {
                        "id": req_id,
                        "method": method,
                        "url": url,
                        "status_code": status_code,
                        "status_text": status_text,
                        "timestamp": datetime.now().isoformat(),
                        "source": "mcp_playwright",
                        "domain": domain,
                        "path": path,
                        "query_params": query_params,
                        "raw_line": line
                    }
                    network_logs.append(network_log)

                except Exception as e:
                    self.logger.debug(f"Failed to parse network log line: {line}, error: {e}")
                    continue

            # Also capture any JSON-formatted network data
            elif line.startswith('{') and '"raw_request"' in line:
                try:
                    network_json = json.loads(line)
                    if isinstance(network_json, dict):
                        network_json["timestamp"] = datetime.now().isoformat()
                        network_json["source"] = "mcp_playwright_json"
                        network_logs.append(network_json)
                except:
                    continue

        self.logger.info(f"📊 Parsed {len(network_logs)} network requests from MCP data")
        return network_logs

    def _extract_mcp_raw_requests(self, network_data: str) -> List[Dict[str, Any]]:
        """Extract raw HTTP requests from MCP Playwright network data."""
        raw_requests = []
        lines = network_data.split('\n')

        # Look for JSON-formatted raw request data from browser_network_request_details
        current_json = ""
        in_json_block = False

        for line in lines:
            line = line.strip()

            # Detect start of JSON block
            if line.startswith('{') and ('"raw_request"' in line or '"raw_response"' in line):
                in_json_block = True
                current_json = line
            elif in_json_block:
                current_json += line
                # Detect end of JSON block
                if line.endswith('}'):
                    try:
                        request_data = json.loads(current_json)
                        if isinstance(request_data, dict):
                            # Enhance with metadata
                            request_data.update({
                                "timestamp": datetime.now().isoformat(),
                                "source": "mcp_playwright_raw",
                                "complete": True
                            })
                            raw_requests.append(request_data)
                    except json.JSONDecodeError:
                        pass
                    finally:
                        in_json_block = False
                        current_json = ""

            # Also extract from simple network request lines
            elif '[ID:' in line and '=>' in line:
                try:
                    # Extract basic request info
                    id_match = re.search(r'\[ID:\s*(\d+)\]', line)
                    req_id = id_match.group(1) if id_match else "unknown"

                    method_match = re.search(r'\]\s*\[([A-Z]+)\]', line)
                    method = method_match.group(1) if method_match else "GET"

                    url_match = re.search(r'\]\s*([^\s]+)\s*=>', line)
                    url = url_match.group(1) if url_match else "unknown"

                    status_match = re.search(r'=>\s*\[(\d+)\]', line)
                    status_code = status_match.group(1) if status_match else "unknown"

                    if url != "unknown":
                        parsed_url = urlparse(url)
                        path = parsed_url.path or '/'
                        if parsed_url.query:
                            path += f"?{parsed_url.query}"

                        # Build basic raw HTTP request
                        headers = {
                            "Host": parsed_url.netloc,
                            "User-Agent": "Mozilla/5.0 (compatible; Browser-Agent/1.0)",
                            "Accept": "*/*",
                            "Accept-Language": "en-US,en;q=0.9",
                            "Accept-Encoding": "gzip, deflate, br",
                            "Connection": "keep-alive"
                        }

                        raw_http_lines = [f"{method} {path} HTTP/1.1"]
                        for header_name, header_value in headers.items():
                            raw_http_lines.append(f"{header_name}: {header_value}")
                        raw_http_lines.append("")  # Empty line

                        raw_http = "\r\n".join(raw_http_lines)

                        raw_request = {
                            "id": req_id,
                            "method": method,
                            "url": url,
                            "status_code": status_code,
                            "raw_request": raw_http,
                            "headers": headers,
                            "body": "",
                            "timestamp": datetime.now().isoformat(),
                            "complete": False,
                            "source": "mcp_playwright_basic"
                        }
                        raw_requests.append(raw_request)

                except Exception as e:
                    self.logger.debug(f"Failed to extract raw request from line: {line}, error: {e}")
                    continue

        self.logger.info(f"📊 Extracted {len(raw_requests)} raw HTTP requests")
        return raw_requests
            
    def _parse_console_data_enhanced(self, console_data: str) -> List[Dict[str, Any]]:
        """Enhanced parsing of console data from MCP Playwright output."""
        console_logs = []

        try:
            lines = console_data.split('\n')

            for line in lines:
                line = line.strip()
                if not line or len(line) < 3:
                    continue

                # Skip common non-console lines
                if any(skip in line for skip in ['===', '```', 'browser_console_messages', 'Use browser_', 'Ran Playwright code']):
                    continue

                # Look for actual console messages with [LOG], [DEBUG], [ERROR] etc.
                console_match = re.match(r'^\[([A-Z]+)\]\s*(.*)', line)
                if console_match:
                    level = console_match.group(1).lower()
                    message = console_match.group(2)

                    # Extract URL information if present
                    url = ""
                    url_match = re.search(r'https?://[^\s]+', message)
                    if url_match:
                        url = url_match.group()

                    # Extract line:column information
                    line_number = None
                    column = None
                    line_col_match = re.search(r':(\d+):(\d+)', message)
                    if line_col_match:
                        line_number = int(line_col_match.group(1))
                        column = int(line_col_match.group(2))

                    # Extract stack trace information
                    stack_trace = ""
                    if "at " in message and ("http" in message or "file:" in message):
                        stack_trace = message

                    console_log = {
                        "timestamp": datetime.now().isoformat(),
                        "message": message,
                        "level": level,
                        "source": "mcp_playwright_enhanced",
                        "url": url,
                        "line": line_number,
                        "column": column,
                        "stack_trace": stack_trace,
                        "raw_line": line
                    }
                    console_logs.append(console_log)

                # Also capture lines that look like console output but don't have brackets
                elif any(keyword in line.lower() for keyword in ["error", "warning", "debug", "log", "console", "exception"]):
                    # Determine level from content
                    level = "info"
                    if any(keyword in line.lower() for keyword in ["error", "exception", "failed"]):
                        level = "error"
                    elif any(keyword in line.lower() for keyword in ["warn", "warning"]):
                        level = "warn"
                    elif "debug" in line.lower():
                        level = "debug"

                    console_log = {
                        "timestamp": datetime.now().isoformat(),
                        "message": line,
                        "level": level,
                        "source": "mcp_playwright_enhanced",
                        "url": "",
                        "line": None,
                        "column": None,
                        "stack_trace": "",
                        "raw_line": line
                    }
                    console_logs.append(console_log)

            self.logger.info(f"📊 Enhanced console parsing found {len(console_logs)} console messages")
            return console_logs

        except Exception as e:
            self.logger.error(f"❌ Enhanced console parsing failed: {e}")
            return []

    def _parse_mcp_console_logs(self, console_data: str) -> List[Dict[str, Any]]:
        """Parse console logs from MCP Playwright browser_console_messages output."""
        console_logs = []
        lines = console_data.split('\n')

        for line in lines:
            line = line.strip()
            if not line or len(line) < 3:
                continue

            # Skip common non-console lines
            if any(skip in line for skip in ['===', '```', 'browser_console_messages', 'Use browser_']):
                continue

            # Parse MCP console message format: [LEVEL] message
            level = "info"
            message = line

            # Extract log level from MCP format
            level_match = re.match(r'^\[([A-Z]+)\]\s*(.*)', line)
            if level_match:
                level = level_match.group(1).lower()
                message = level_match.group(2)
            else:
                # Determine level from content keywords
                if any(keyword in line.lower() for keyword in ["error", "failed", "exception", "uncaught"]):
                    level = "error"
                elif any(keyword in line.lower() for keyword in ["warn", "warning", "deprecated"]):
                    level = "warn"
                elif any(keyword in line.lower() for keyword in ["debug", "trace"]):
                    level = "debug"
                elif any(keyword in line.lower() for keyword in ["log", "console"]):
                    level = "log"

            # Extract URL information if present
            url = ""
            url_match = re.search(r'https?://[^\s]+', message)
            if url_match:
                url = url_match.group()

            # Extract line:column information
            line_number = None
            column = None
            line_col_match = re.search(r':(\d+):(\d+)', message)
            if line_col_match:
                line_number = int(line_col_match.group(1))
                column = int(line_col_match.group(2))

            # Extract stack trace information
            stack_trace = ""
            if "at " in message and ("http" in message or "file:" in message):
                stack_trace = message

            console_log = {
                "timestamp": datetime.now().isoformat(),
                "message": message,
                "level": level,
                "source": "mcp_playwright",
                "url": url,
                "line": line_number,
                "column": column,
                "stack_trace": stack_trace,
                "raw_line": line
            }
            console_logs.append(console_log)

        self.logger.info(f"📊 Parsed {len(console_logs)} console messages from MCP data")
        return console_logs

    def _analyze_application_type(self, page_content: str, target_url: str = "") -> Dict[str, Any]:
        """Analyze application type based on page content and URL patterns."""
        analysis = {
            "detected_type": "unknown",
            "confidence": 0.0,
            "features": [],
            "technology_stack": [],
            "reasoning": "",
            "url_indicators": []
        }

        content_lower = page_content.lower()

        # Application type indicators
        indicators = {
            "ecommerce": ["cart", "checkout", "product", "price", "buy", "shop", "payment", "order"],
            "admin/dashboard": ["dashboard", "admin", "manage", "users", "settings", "analytics", "control panel"],
            "cms/blog": ["post", "article", "blog", "content", "publish", "editor", "wordpress"],
            "social/community": ["profile", "friends", "message", "feed", "community", "social", "chat"],
            "login_page": ["log in", "sign in", "password", "username", "login", "authentication"],
            "api/service": ["api", "endpoint", "json", "rest", "graphql", "service", "documentation"],
            "portfolio": ["portfolio", "resume", "cv", "projects", "skills", "experience"],
            "corporate": ["about us", "company", "services", "contact", "business", "enterprise"]
        }

        scores = {key: sum(1 for ind in val if ind in content_lower) for key, val in indicators.items()}

        # Technology stack detection
        tech_indicators = {
            "react": ["react", "jsx", "reactjs"],
            "angular": ["angular", "ng-", "angularjs"],
            "vue": ["vue", "v-", "vuejs"],
            "jquery": ["jquery", "$."],
            "bootstrap": ["bootstrap", "bs-"],
            "wordpress": ["wp-content", "wordpress", "wp-"],
            "drupal": ["drupal", "node/"],
            "django": ["django", "csrf"],
            "flask": ["flask"],
            "express": ["express", "nodejs"],
            "php": ["php", "<?php"],
            "asp.net": ["asp.net", "aspx", "viewstate"]
        }

        for tech, tech_inds in tech_indicators.items():
            if any(ind in content_lower for ind in tech_inds):
                analysis["technology_stack"].append(tech)

        # URL-based indicators
        if target_url:
            url_lower = target_url.lower()
            if "admin" in url_lower:
                analysis["url_indicators"].append("admin_panel")
                scores["admin/dashboard"] += 2
            if "api" in url_lower:
                analysis["url_indicators"].append("api_service")
                scores["api/service"] += 2
            if "blog" in url_lower:
                analysis["url_indicators"].append("blog")
                scores["cms/blog"] += 1

        # Determine final type
        max_score = max(scores.values()) if scores else 0
        if max_score > 0:
            detected_type = max(scores, key=scores.get)
            analysis["detected_type"] = detected_type
            analysis["confidence"] = min(max_score / 6.0, 1.0)  # Adjusted for more indicators
            analysis["reasoning"] = f"Detected {detected_type} based on {max_score} matching indicators."

            # Add specific features found
            analysis["features"] = [ind for ind in indicators[detected_type] if ind in content_lower]
        else:
            analysis["reasoning"] = "No strong indicators for a specific application type were found."

        return analysis